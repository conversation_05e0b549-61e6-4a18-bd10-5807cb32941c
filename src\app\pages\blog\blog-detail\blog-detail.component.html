<app-header></app-header>

<div class="blog-detail-container">
  <div class="container mx-auto px-4 py-8">
    <!-- Loading State -->
    <div *ngIf="isLoading" class="loading-container">
      <mat-spinner diameter="40"></mat-spinner>
      <p class="loading-text">Loading article...</p>
    </div>

    <!-- Blog Post Content -->
    <article *ngIf="!isLoading && post" class="blog-post">
      <!-- Post Header -->
      <header class="post-header">
        <div class="post-meta">
          <span class="post-category">{{ post.category }}</span>
          <span class="post-date">{{ post.publishDate | date:'mediumDate' }}</span>
        </div>
        <h1 class="post-title">{{ post.title }}</h1>
        <div class="post-author">
          <img [src]="post.authorAvatar || 'assets/images/avatars/default-avatar.jpg'" [alt]="post.author" class="author-avatar">
          <div class="author-info">
            <span class="author-name">{{ post.author }}</span>
            <span class="author-title">{{ post.authorTitle || 'Contributor' }}</span>
          </div>
        </div>
      </header>

      <!-- Featured Image -->
      <div class="featured-image">
        <img [src]="post.imageUrl" [alt]="post.title" class="w-full h-full object-cover">
      </div>

      <!-- Post Content -->
      <div class="post-content" [innerHTML]="post.content"></div>

      <!-- Tags -->
      <div class="post-tags">
        <span class="tag-label">Tags:</span>
        <div class="tags-list">
          <a *ngFor="let tag of post.tags" [routerLink]="['/blogs/audiophile-101/tag', tag]" class="tag">{{ tag }}</a>
        </div>
      </div>

      <!-- Share Buttons -->
      <div class="share-container">
        <span class="share-label">Share this article:</span>
        <div class="share-buttons">
          <button mat-icon-button color="primary" (click)="shareOnFacebook()" aria-label="Share on Facebook">
            <mat-icon>facebook</mat-icon>
          </button>
          <button mat-icon-button color="primary" (click)="shareOnTwitter()" aria-label="Share on Twitter">
            <mat-icon>twitter</mat-icon>
          </button>
          <button mat-icon-button color="primary" (click)="shareOnLinkedIn()" aria-label="Share on LinkedIn">
            <mat-icon>linkedin</mat-icon>
          </button>
        </div>
      </div>
    </article>

    <!-- Related Posts -->
    <section *ngIf="!isLoading && relatedPosts.length > 0" class="related-posts">
      <h2 class="section-title">Related Articles</h2>
      <div class="grid grid-cols-1 md:grid-cols-3 gap-6">
        <div *ngFor="let relatedPost of relatedPosts" class="related-post-card">
          <a [routerLink]="['/blogs/audiophile-101', relatedPost.slug]" class="post-card">
            <div class="post-image">
              <img [src]="relatedPost.imageUrl" [alt]="relatedPost.title" class="w-full h-full object-cover">
            </div>
            <div class="post-content">
              <h3 class="post-title">{{ relatedPost.title }}</h3>
              <p class="post-excerpt">{{ relatedPost.excerpt }}</p>
              <div class="post-meta">
                <span class="post-date">{{ relatedPost.publishDate | date:'mediumDate' }}</span>
              </div>
            </div>
          </a>
        </div>
      </div>
    </section>

    <!-- Newsletter -->
    <section class="blog-newsletter">
      <div class="newsletter-container">
        <div class="newsletter-content">
          <h2 class="newsletter-title">Enjoyed this article?</h2>
          <p class="newsletter-description">
            Subscribe to our newsletter to receive the latest articles, product reviews, and audiophile news.
          </p>
          <div class="newsletter-form">
            <mat-form-field appearance="outline" class="email-field">
              <mat-label>Email Address</mat-label>
              <input matInput type="email" placeholder="<EMAIL>">
            </mat-form-field>
            <button mat-raised-button color="primary" class="subscribe-button">
              Subscribe
            </button>
          </div>
        </div>
      </div>
    </section>
  </div>
</div>

<app-footer></app-footer>
