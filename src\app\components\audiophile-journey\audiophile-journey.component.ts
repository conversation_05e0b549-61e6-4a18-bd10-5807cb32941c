import { Component } from '@angular/core';
import { CommonModule } from '@angular/common';
import { RouterModule } from '@angular/router';
import { MaterialModule } from '../../material/material.module';

interface CategoryItem {
  name: string;
  icon: string;
  link: string;
}

@Component({
  selector: 'app-audiophile-journey',
  standalone: true,
  imports: [
    CommonModule,
    RouterModule,
    MaterialModule
  ],
  templateUrl: './audiophile-journey.component.html',
  styleUrl: './audiophile-journey.component.scss'
})
export class AudiophileJourneyComponent {
  categories: CategoryItem[] = [
    {
      name: 'IN-EARS FOR BEGINNERS',
      icon: 'assets/images/journey/placeholder-icon.svg',
      link: '/collections/beginner-audiophile-iems'
    },
    {
      name: 'HEADPHONES FOR BEGINNERS',
      icon: 'assets/images/journey/placeholder-icon.svg',
      link: '/collections/beginner-audiophile-headphones'
    },
    {
      name: 'TRUE WIRELESS EARBUDS',
      icon: 'assets/images/journey/placeholder-icon.svg',
      link: '/collections/true-wireless-earbuds'
    },
    {
      name: 'PORTABLE DACS & AMPS',
      icon: 'assets/images/journey/placeholder-icon.svg',
      link: '/collections/portable-dacs-amps'
    },
    {
      name: 'DESKTOP DACS & AMPS',
      icon: 'assets/images/journey/placeholder-icon.svg',
      link: '/collections/desktop-dacs-amps'
    },
    {
      name: 'WIRELESS HEADPHONES',
      icon: 'assets/images/journey/placeholder-icon.svg',
      link: '/collections/wireless-headphones'
    },
    {
      name: 'AUDIOPHILE ACCESSORIES',
      icon: 'assets/images/journey/placeholder-icon.svg',
      link: '/collections/accessories'
    },
    {
      name: 'CABLES & ADAPTERS',
      icon: 'assets/images/journey/placeholder-icon.svg',
      link: '/collections/cables-adapters'
    },
    {
      name: 'CASES & STANDS',
      icon: 'assets/images/journey/placeholder-icon.svg',
      link: '/collections/cases-stands'
    },
    {
      name: 'BLUETOOTH DACS',
      icon: 'assets/images/journey/placeholder-icon.svg',
      link: '/collections/bluetooth-dacs'
    },
    {
      name: 'PORTABLE PLAYERS',
      icon: 'assets/images/journey/placeholder-icon.svg',
      link: '/collections/portable-players'
    },
    {
      name: 'EARTIPS & EARPADS',
      icon: 'assets/images/journey/placeholder-icon.svg',
      link: '/collections/eartips-earpads'
    },
    {
      name: 'BALANCED CABLES',
      icon: 'assets/images/journey/placeholder-icon.svg',
      link: '/collections/balanced-cables'
    },
    {
      name: 'SPEAKERS',
      icon: 'assets/images/journey/placeholder-icon.svg',
      link: '/collections/speakers'
    }
  ];
}
