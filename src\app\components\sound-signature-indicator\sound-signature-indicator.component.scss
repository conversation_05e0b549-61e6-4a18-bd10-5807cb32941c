.sound-signature-container {
  background-color: #f9f9f9;
  border-radius: 8px;
  padding: 1rem;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);

  &.small {
    padding: 0.5rem;
    font-size: 0.8rem;

    .signature-header {
      margin-bottom: 0.5rem;
    }

    .signature-label {
      font-size: 0.9rem;

      .signature-icon {
        font-size: 1rem;
        height: 1rem;
        width: 1rem;
      }
    }

    .signature-description {
      font-size: 0.7rem;
    }

    .frequency-graph {
      height: 60px;
    }

    .bar-label, .bar-value {
      font-size: 0.7rem;
    }
  }

  &.medium {
    padding: 1rem;
    font-size: 0.9rem;

    .signature-header {
      margin-bottom: 0.75rem;
    }

    .signature-label {
      font-size: 1rem;

      .signature-icon {
        font-size: 1.2rem;
        height: 1.2rem;
        width: 1.2rem;
      }
    }

    .signature-description {
      font-size: 0.8rem;
    }

    .frequency-graph {
      height: 80px;
    }

    .bar-label, .bar-value {
      font-size: 0.8rem;
    }
  }

  &.large {
    padding: 1.5rem;
    font-size: 1rem;

    .signature-header {
      margin-bottom: 1rem;
    }

    .signature-label {
      font-size: 1.2rem;

      .signature-icon {
        font-size: 1.4rem;
        height: 1.4rem;
        width: 1.4rem;
      }
    }

    .signature-description {
      font-size: 0.9rem;
    }

    .frequency-graph {
      height: 100px;
    }

    .bar-label, .bar-value {
      font-size: 0.9rem;
    }
  }

  .music-lovers-counter {
    opacity: 0;
    transform: translateY(20px);
    transition: all 0.6s ease-out;

    &.visible {
      opacity: 1;
      transform: translateY(0);
    }

    .counter-value {
      background: linear-gradient(45deg, var(--primary), #ff6b6b);
      -webkit-background-clip: text;
      -webkit-text-fill-color: transparent;
      font-family: 'Montserrat', sans-serif;
    }
  }
}

.signature-header {
  margin-bottom: 1rem;
}

.signature-label {
  display: flex;
  align-items: center;
  font-weight: 600;
  color: #333;
  margin-bottom: 0.25rem;

  .signature-icon {
    margin-right: 0.5rem;
    color: #666;
  }
}

.signature-description {
  color: #666;
  font-size: 0.85rem;
  line-height: 1.4;
}

.frequency-graph {
  display: flex;
  justify-content: space-between;
  align-items: flex-end;
  height: 80px;
  position: relative;
}

.frequency-bar {
  display: flex;
  flex-direction: column;
  align-items: center;
  flex: 1;
  height: 100%;

  .bar-label {
    font-size: 0.8rem;
    color: #666;
    margin-bottom: 0.5rem;
  }

  .bar-container {
    width: 80%;
    height: 100%;
    background-color: #e0e0e0;
    border-radius: 4px;
    overflow: hidden;
    position: relative;
  }

  .bar-fill {
    position: absolute;
    bottom: 0;
    left: 0;
    width: 100%;
    background: linear-gradient(to top, #ff5722, #ff9800);
    border-radius: 4px;
    transition: height 0.3s ease;
  }

  .bar-value {
    font-size: 0.8rem;
    color: #666;
    margin-top: 0.5rem;
    font-weight: 600;
  }
}

// Sound signature specific colors
.balanced .bar-fill {
  background: linear-gradient(to top, #4caf50, #8bc34a);
}

.warm .bar-fill {
  background: linear-gradient(to top, #ff9800, #ffeb3b);
}

.bright .bar-fill {
  background: linear-gradient(to top, #2196f3, #03a9f4);
}

.v-shaped .bar-fill {
  background: linear-gradient(to top, #9c27b0, #e91e63);
}

.bass-focused .bar-fill {
  background: linear-gradient(to top, #ff5722, #ff9800);
}

.vocal-focused .bar-fill {
  background: linear-gradient(to top, #3f51b5, #2196f3);
}

.neutral .bar-fill {
  background: linear-gradient(to top, #607d8b, #90a4ae);
}

.music-lovers-container {
  padding: 3rem;
  background-color: #ffffff;
  border-radius: 1rem;
  box-shadow: 0 4px 6px rgba(0, 0, 0, 0.05);
  opacity: 0;
  transform: translateY(20px);
  transition: all 0.8s cubic-bezier(0.4, 0, 0.2, 1);

  &.visible {
    opacity: 1;
    transform: translateY(0);
  }

  .icon-container {
    display: flex;
    justify-content: center;
    align-items: center;
    width: 80px;
    height: 80px;
    margin: 0 auto;
    border-radius: 50%;
    background: linear-gradient(135deg, var(--primary-rgb, 59, 130, 246) 0%, rgba(var(--primary-rgb, 59, 130, 246), 0.1) 100%);

    mat-icon {
      font-size: 40px;
      height: 40px;
      width: 40px;
    }
  }

  .counter-value {
    background: linear-gradient(135deg, var(--primary, #3b82f6) 0%, #6366f1 100%);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    font-family: 'Montserrat', sans-serif;
    letter-spacing: -0.02em;
  }
}