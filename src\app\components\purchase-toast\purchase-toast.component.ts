import { Component, Input, OnInit, OnDestroy } from '@angular/core';
import { CommonModule } from '@angular/common';
import { MaterialModule } from '../../material/material.module';
import { ToastService } from '../../services/toast.service';
import { PurchaseNotification } from '../../models/product.model';
import { Observable, Subject } from 'rxjs';
import { takeUntil } from 'rxjs/operators';
import {
  animate,
  style,
  transition,
  trigger,
  state,
} from '@angular/animations';

@Component({
  selector: 'app-purchase-toast',
  standalone: true,
  imports: [CommonModule, MaterialModule],
  templateUrl: './purchase-toast.component.html',
  styleUrl: './purchase-toast.component.scss',
  animations: [
    trigger('toastAnimation', [
      state(
        'void',
        style({
          transform: 'translateY(100%)',
          opacity: 0,
        })
      ),
      state(
        'visible',
        style({
          transform: 'translateY(0)',
          opacity: 1,
        })
      ),
      transition('void => visible', [
        animate('300ms cubic-bezier(0.4, 0, 0.2, 1)'),
      ]),
      transition('visible => void', [
        animate('200ms cubic-bezier(0.4, 0, 0.2, 1)'),
      ]),
    ]),
    trigger('iconAnimation', [
      transition(':enter', [
        style({ transform: 'scale(0)' }),
        animate(
          '400ms cubic-bezier(0.4, 0, 0.2, 1)',
          style({ transform: 'scale(1)' })
        ),
      ]),
    ]),
  ],
})
export class PurchaseToastComponent implements OnInit, OnDestroy {
  notification$!: Observable<PurchaseNotification | null>;
  isActive$!: Observable<boolean>;
  private destroy$ = new Subject<void>();

  constructor(private toastService: ToastService) {}

  ngOnInit(): void {
    this.notification$ = this.toastService.notifications$;
    this.isActive$ = this.toastService.isActive$;
  }

  ngOnDestroy(): void {
    this.destroy$.next();
    this.destroy$.complete();
  }

  getIcon(notification: PurchaseNotification): string {
    switch (notification.type) {
      case 'copy':
        return 'content_copy';
      case 'stock-warning':
        return 'inventory';
      case 'order-confirmation':
        return 'check_circle';
      case 'error':
        return 'error';
      case 'viewer':
        return 'visibility';
      default:
        return 'shopping_cart';
    }
  }

  getIconClass(notification: PurchaseNotification): string {
    switch (notification.type) {
      case 'copy':
        return 'text-blue-500';
      case 'stock-warning':
        return 'text-orange-500';
      case 'order-confirmation':
        return 'text-green-500';
      case 'error':
        return 'text-red-500';
      case 'viewer':
        return 'text-purple-500';
      default:
        return 'text-primary';
    }
  }

  getBackgroundClass(notification: PurchaseNotification): string {
    switch (notification.type) {
      case 'copy':
        return 'bg-blue-50';
      case 'stock-warning':
        return 'bg-orange-50';
      case 'order-confirmation':
        return 'bg-green-50';
      case 'error':
        return 'bg-red-50';
      case 'viewer':
        return 'bg-purple-50';
      default:
        return 'bg-white';
    }
  }

  formatTimeAgo(timestamp: Date): string {
    const now = new Date();
    const seconds = Math.floor((now.getTime() - timestamp.getTime()) / 1000);

    if (seconds < 60) {
      return 'just now';
    }

    const minutes = Math.floor(seconds / 60);
    if (minutes < 60) {
      return `${minutes}m ago`;
    }

    const hours = Math.floor(minutes / 60);
    if (hours < 24) {
      return `${hours}h ago`;
    }

    const days = Math.floor(hours / 24);
    return `${days}d ago`;
  }

  // Helper function to handle sound effects (if enabled)
  private playSound(type: string): void {
    // Implement sound effects based on notification type
    // This could be expanded based on user preferences
  }
}
