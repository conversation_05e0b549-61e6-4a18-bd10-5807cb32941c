.account-container {
  background-color: #f9fafb;
  min-height: calc(100vh - 200px);
}

.page-header {
  text-align: center;
}

.page-title {
  font-size: 2rem;
  font-weight: 600;
  color: var(--text-primary);
  margin-bottom: 0.5rem;
}

.page-subtitle {
  color: var(--text-secondary);
  font-size: 1rem;
}

.account-content {
  display: grid;
  grid-template-columns: 280px 1fr;
  gap: 2rem;
  
  @media (max-width: 768px) {
    grid-template-columns: 1fr;
  }
}

// Sidebar Styles
.account-sidebar {
  background-color: white;
  border-radius: 8px;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
  padding: 1.5rem;
  height: fit-content;
}

.user-info {
  display: flex;
  align-items: center;
  margin-bottom: 1.5rem;
  padding-bottom: 1.5rem;
  border-bottom: 1px solid #e5e7eb;
}

.avatar {
  width: 60px;
  height: 60px;
  border-radius: 50%;
  overflow: hidden;
  margin-right: 1rem;
  
  img {
    width: 100%;
    height: 100%;
    object-fit: cover;
  }
}

.user-name {
  font-size: 1.125rem;
  font-weight: 600;
  color: var(--text-primary);
  margin-bottom: 0.25rem;
}

.user-email {
  font-size: 0.875rem;
  color: var(--text-secondary);
}

.account-nav {
  margin-bottom: 1.5rem;
  
  ul {
    list-style: none;
    padding: 0;
    margin: 0;
  }
}

.nav-item {
  display: flex;
  align-items: center;
  width: 100%;
  padding: 0.75rem 1rem;
  margin-bottom: 0.5rem;
  border-radius: 6px;
  color: var(--text-primary);
  background: transparent;
  border: none;
  cursor: pointer;
  transition: all 0.2s ease;
  text-align: left;
  
  mat-icon {
    margin-right: 0.75rem;
    color: var(--text-secondary);
  }
  
  &:hover {
    background-color: rgba(0, 0, 0, 0.04);
  }
  
  &.active {
    background-color: var(--primary);
    color: white;
    
    mat-icon {
      color: white;
    }
  }
}

.logout-button {
  width: 100%;
}

// Main Content Styles
.account-main {
  background-color: white;
  border-radius: 8px;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
  padding: 2rem;
}

.tab-header {
  margin-bottom: 2rem;
}

.tab-title {
  font-size: 1.5rem;
  font-weight: 600;
  color: var(--text-primary);
  margin-bottom: 0.5rem;
}

.tab-subtitle {
  color: var(--text-secondary);
  font-size: 0.875rem;
}

// Empty States
.empty-state {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 3rem 1rem;
  text-align: center;
}

.empty-icon {
  width: 120px;
  height: 120px;
  margin-bottom: 1.5rem;
}

.empty-title {
  font-size: 1.25rem;
  font-weight: 600;
  color: var(--text-primary);
  margin-bottom: 0.5rem;
}

.empty-message {
  color: var(--text-secondary);
  margin-bottom: 1.5rem;
  max-width: 400px;
}

// Addresses
.addresses-container {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(300px, 1fr));
  gap: 1.5rem;
}

.address-card {
  border: 1px dashed #e5e7eb;
  border-radius: 8px;
  padding: 1.5rem;
  height: 200px;
  display: flex;
  align-items: center;
  justify-content: center;
}

.add-address-button {
  height: 48px;
  font-size: 1rem;
}

// Profile Form
.profile-form {
  max-width: 600px;
}

.form-row {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 1rem;
  
  @media (max-width: 480px) {
    grid-template-columns: 1fr;
  }
}

.form-actions {
  margin-top: 1.5rem;
}

// Settings
.settings-section {
  margin-bottom: 2rem;
  padding-bottom: 2rem;
  border-bottom: 1px solid #e5e7eb;
  
  &:last-child {
    border-bottom: none;
    margin-bottom: 0;
    padding-bottom: 0;
  }
}

.settings-title {
  font-size: 1.125rem;
  font-weight: 600;
  color: var(--text-primary);
  margin-bottom: 1rem;
}

.settings-content {
  display: flex;
  flex-direction: column;
  gap: 1rem;
}

.settings-description {
  color: var(--text-secondary);
  margin-bottom: 1rem;
  font-size: 0.875rem;
}
