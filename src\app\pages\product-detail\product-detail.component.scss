// Product Images
.product-images {
  .main-image {
    aspect-ratio: 1/1;
    display: flex;
    align-items: center;
    justify-content: center;
    padding: 1rem;
    
    img {
      max-height: 100%;
      object-fit: contain;
    }
  }
  
  .thumbnail {
    aspect-ratio: 1/1;
    cursor: pointer;
    transition: all 0.2s ease;
    
    &:hover {
      border-color: var(--primary);
    }
    
    &.border-primary {
      border-width: 2px;
    }
  }
}

// Product Info
.product-info {
  h1 {
    line-height: 1.3;
  }
}

// Quantity Input
input[type="number"] {
  -moz-appearance: textfield;
  
  &::-webkit-outer-spin-button,
  &::-webkit-inner-spin-button {
    -webkit-appearance: none;
    margin: 0;
  }
}

// Tab Group Styling
::ng-deep {
  .mat-mdc-tab-group {
    .mat-mdc-tab-header {
      border-bottom: 1px solid #e5e7eb;
    }
    
    .mat-mdc-tab {
      &.mdc-tab--active {
        .mdc-tab__text-label {
          color: var(--primary);
          font-weight: 600;
        }
      }
    }
    
    .mat-mdc-tab-body-content {
      padding: 1rem 0;
    }
  }
}

// Related Products
.product-card {
  position: relative;
  border: 1px solid #e5e7eb;
  border-radius: 8px;
  overflow: hidden;
  transition: all 0.3s ease;
  
  &:hover {
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.05);
    transform: translateY(-5px);
  }
  
  .product-image {
    padding: 1rem;
    background-color: #f9fafb;
    aspect-ratio: 1/1;
    display: flex;
    align-items: center;
    justify-content: center;
    
    img {
      max-height: 100%;
      transition: transform 0.5s ease;
    }
    
    &:hover img {
      transform: scale(1.05);
    }
  }
  
  .product-info {
    padding: 1rem;
    
    .product-title {
      font-weight: 500;
      font-size: 1rem;
      margin-bottom: 0.5rem;
      line-height: 1.4;
      display: -webkit-box;
      -webkit-line-clamp: 2;
      -webkit-box-orient: vertical;
      overflow: hidden;
      
      a {
        color: #1f2937;
        text-decoration: none;
        
        &:hover {
          color: var(--primary);
        }
      }
    }
    
    .product-price {
      display: flex;
      flex-wrap: wrap;
      align-items: baseline;
      gap: 0.5rem;
      
      .original-price {
        color: #9ca3af;
        text-decoration: line-through;
        font-size: 0.875rem;
      }
      
      .current-price {
        color: var(--primary);
        font-weight: 600;
        font-size: 1.125rem;
      }
    }
  }
}

// Breadcrumb
.breadcrumb {
  a {
    transition: color 0.2s ease;
    
    &:hover {
      color: var(--primary);
    }
  }
}
