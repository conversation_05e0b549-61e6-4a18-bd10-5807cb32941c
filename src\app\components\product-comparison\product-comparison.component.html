<section class="product-comparison py-12 bg-white">
  <div class="container mx-auto px-4">
    <div class="text-center mb-8">
      <h2 class="section-title inline-block">Product Comparison</h2>
      <p class="text-gray-600 mt-3 max-w-2xl mx-auto">Compare features and specifications to find the perfect audio gear for your needs.</p>
    </div>

    <!-- Product Selection -->
    <div class="mb-8">
      <div class="flex flex-wrap items-center justify-between mb-4">
        <h3 class="text-lg font-semibold">Selected Products ({{ (selectedProducts$ | async)?.length || 0 }}/4)</h3>
        <button mat-stroked-button color="warn" (click)="clearComparison()" [disabled]="(selectedProducts$ | async)?.length === 0">
          <mat-icon class="mr-1">delete</mat-icon>
          Clear All
        </button>
      </div>

      <!-- Product Selector -->
      <ng-container *ngIf="selectedProducts$ | async as selectedProducts">
        <mat-form-field appearance="outline" class="w-full">
          <mat-label>Add product to compare</mat-label>
          <mat-select placeholder="Select a product" [disabled]="selectedProducts.length >= 4">
            <ng-container *ngIf="allProducts$ | async as products">
              <mat-option *ngFor="let product of products" [value]="product" (click)="addProductToCompare(product)">
                {{ product.name }}
              </mat-option>
            </ng-container>
          </mat-select>
        </mat-form-field>
      </ng-container>
    </div>

    <!-- Comparison Table -->
    <div class="overflow-x-auto">
      <table class="comparison-table w-full border-collapse">
        <thead>
          <tr>
            <th class="sticky-col bg-gray-50 border-r border-gray-200 min-w-[200px]">Product</th>
            <ng-container *ngIf="selectedProducts$ | async as products">
              <th *ngFor="let product of products" class="product-col border-b border-gray-200 p-4 text-center min-w-[200px]">
                <div class="relative">
                  <button mat-icon-button color="warn" class="absolute top-0 right-0" (click)="removeProductFromCompare(product.id)">
                    <mat-icon>close</mat-icon>
                  </button>
                  <div class="product-image mb-3">
                    <img [src]="product.imageUrl" [alt]="product.name" class="mx-auto h-32 object-contain">
                  </div>
                  <h4 class="font-medium text-sm mb-1">{{ product.name }}</h4>
                  <div class="flex justify-center items-center mb-2">
                    <div class="flex">
                      <mat-icon *ngFor="let i of [1, 2, 3, 4, 5]" class="text-amber-400 text-sm">
                        {{ i <= product.rating ? 'star' : (i - 0.5 <= product.rating ? 'star_half' : 'star_border') }}
                      </mat-icon>
                    </div>
                  </div>
                  <div class="price-container mb-3">
                    <span *ngIf="product.discountedPrice" class="text-gray-400 line-through text-xs mr-1">
                      ₹{{ product.price.toFixed(2) }}
                    </span>
                    <span class="text-primary font-semibold">
                      ₹{{ (product.discountedPrice || product.price).toFixed(2) }}
                    </span>
                  </div>
                  <button mat-raised-button color="primary" class="w-full" (click)="addToCart(product)">
                    Add to Cart
                  </button>
                </div>
              </th>
              <th *ngIf="products.length === 0" class="border-b border-gray-200 p-4 text-center">
                <div class="text-gray-500 italic">No products selected</div>
              </th>
            </ng-container>
          </tr>
        </thead>

        <tbody>
          <ng-container *ngFor="let category of comparisonCategories">
            <!-- Category Header -->
            <tr>
              <td colspan="100" class="bg-gray-100 font-semibold p-3 border-t border-b border-gray-200">
                {{ category.name }}
              </td>
            </tr>

            <!-- Properties -->
            <ng-container *ngFor="let property of category.properties">
              <tr>
                <td class="sticky-col bg-gray-50 border-r border-gray-200 p-3 font-medium">
                  {{ property.label }}
                </td>
                <ng-container *ngIf="selectedProducts$ | async as products">
                  <td *ngFor="let product of products" class="border-b border-gray-200 p-3 text-center">
                    {{ getPropertyValue(product, property) }}
                  </td>
                  <td *ngIf="products.length === 0" class="border-b border-gray-200 p-3 text-center">
                    <div class="text-gray-500 italic">-</div>
                  </td>
                </ng-container>
              </tr>
            </ng-container>
          </ng-container>
        </tbody>
      </table>
    </div>

    <!-- Empty State -->
    <ng-container *ngIf="selectedProducts$ | async as selectedProducts">
      <div *ngIf="selectedProducts.length === 0" class="text-center py-12 bg-gray-50 rounded-lg mt-8">
        <mat-icon class="text-gray-400 text-5xl mb-4">compare</mat-icon>
        <h3 class="text-xl font-semibold mb-2">No Products to Compare</h3>
        <p class="text-gray-600 mb-4">Add products to start comparing their features and specifications.</p>
        <button mat-raised-button color="primary" routerLink="/collections/all">
          Browse Products
        </button>
      </div>
    </ng-container>
  </div>
</section>
