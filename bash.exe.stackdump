Stack trace:
Frame         Function      Args
0007FFFF3690  00021005FE8E (000210285F68, 00021026AB6E, 000000000000, 0007FFFF2590) msys-2.0.dll+0x1FE8E
0007FFFF3690  0002100467F9 (000000000000, 000000000000, 000000000000, 0007FFFF3968) msys-2.0.dll+0x67F9
0007FFFF3690  000210046832 (000210286019, 0007FFFF3548, 000000000000, 000000000000) msys-2.0.dll+0x6832
0007FFFF3690  000210068CF6 (000000000000, 000000000000, 000000000000, 000000000000) msys-2.0.dll+0x28CF6
0007FFFF3690  000210068E24 (0007FFFF36A0, 000000000000, 000000000000, 000000000000) msys-2.0.dll+0x28E24
0007FFFF3970  00021006A225 (0007FFFF36A0, 000000000000, 000000000000, 000000000000) msys-2.0.dll+0x2A225
End of stack trace
Loaded modules:
000100400000 bash.exe
7FFC6AA80000 ntdll.dll
7FFC69000000 KERNEL32.DLL
7FFC67F80000 KERNELBASE.dll
7FFC691B0000 USER32.dll
7FFC68820000 win32u.dll
7FFC6A460000 GDI32.dll
7FFC68540000 gdi32full.dll
7FFC68780000 msvcp_win.dll
7FFC68660000 ucrtbase.dll
000210040000 msys-2.0.dll
7FFC69800000 advapi32.dll
7FFC68A10000 msvcrt.dll
7FFC6A2D0000 sechost.dll
7FFC68BC0000 RPCRT4.dll
7FFC675D0000 CRYPTBASE.DLL
7FFC68850000 bcryptPrimitives.dll
7FFC6A170000 IMM32.DLL
