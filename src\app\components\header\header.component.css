/* Header Styles */
.header {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  z-index: 1000;
  background-color: #fff;
  transition: box-shadow 0.3s ease;
}

.header.scrolled {
  box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
}

/* Navigation */
.nav-item {
  position: relative;
}

/* Material Icons */
.mat-icon,
.material-icons {
  font-family: 'Material Icons';
  font-weight: normal;
  font-style: normal;
  font-size: 24px;
  line-height: 1;
  letter-spacing: normal;
  text-transform: none;
  display: inline-block;
  white-space: nowrap;
  word-wrap: normal;
  direction: ltr;
  -webkit-font-feature-settings: 'liga';
  -webkit-font-smoothing: antialiased;
  overflow: hidden;
}

/* Dropdown Styles */
.category-dropdown,
.mega-menu {
  display: none;
  animation: fadeIn 0.2s ease-out;
}

.mega-menu {
  padding-left: 0;
  padding-right: 0;
}

.mega-menu > div {
  max-width: 1200px;
  margin: 0 auto;
  padding: 0 1rem;
}

.active-dropdown .category-dropdown,
.active-dropdown .mega-menu {
  display: block;
}

.category-dropdown {
  padding-left: 0;
  padding-right: 0;
}

.category-dropdown > div {
  max-width: 1200px;
  margin: 0 auto;
}

.category-dropdown h3 {
  color: #333;
  font-size: 14px;
  font-weight: 600;
  text-transform: uppercase;
}

.category-dropdown ul li a {
  color: #666;
  font-size: 13px;
  line-height: 1.8;
}

.category-dropdown ul li a:hover {
  color: #2563eb;
}

/* Dropdown Arrow Container */
.dropdown-arrow-container {
  cursor: pointer;
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 4px;
}

.dropdown-arrow-container:hover .dropdown-arrow {
  color: var(--primary);
  border-color: var(--primary);
  background-color: rgba(var(--primary-rgb), 0.05);
}

/* Dropdown Arrow */
.dropdown-arrow {
  cursor: pointer;
  transition: transform 0.2s ease;
  color: #666;
  border: 1px solid #ddd;
  border-radius: 3px;
  padding: 2px;
  font-size: 16px;
  width: 20px;
  height: 20px;
  display: flex;
  align-items: center;
  justify-content: center;
}

.dropdown-arrow:hover {
  color: var(--primary);
  border-color: var(--primary);
  background-color: rgba(var(--primary-rgb), 0.05);
}

.active-dropdown .dropdown-arrow {
  color: var(--primary);
  border-color: var(--primary);
  background-color: rgba(var(--primary-rgb), 0.1);
}

/* Icon buttons */
button[mat-icon-button] {
  transition: transform 0.2s ease;
}

button[mat-icon-button]:hover {
  transform: scale(1.05);
}

/* Search Container */
.search-container {
  transition: border-color 0.2s ease;
}

.search-container:focus-within {
  border-color: var(--primary);
}

/* Mobile Menu */
.nested-accordion .mat-expansion-panel-header {
  padding: 0 16px;
}

.nested-accordion .mat-expansion-panel-body {
  padding: 0 16px 16px;
}

/* Cart Badge Animation */
@keyframes pulse {
  0% {
    transform: scale(1);
  }
  50% {
    transform: scale(1.2);
  }
  100% {
    transform: scale(1);
  }
}

.cart-count {
  animation: pulse 0.3s ease-out;
}

/* Fade In Animation */
@keyframes fadeIn {
  from {
    opacity: 0;
    transform: translateY(-10px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

/* Menu Link Hover Effect */
.menu-link:hover {
  color: var(--primary);
}
