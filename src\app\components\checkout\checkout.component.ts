import { Component, OnInit } from '@angular/core';
import { CommonModule } from '@angular/common';
import { RouterModule } from '@angular/router';
import { MaterialModule } from '../../material/material.module';
import {
  FormsModule,
  ReactiveFormsModule,
  FormGroup,
  FormBuilder,
  Validators,
} from '@angular/forms';
import { CartService } from '../../services/cart.service';
import { CartItem } from '../../models/product.model';
import { Observable, combineLatest } from 'rxjs';
import { map } from 'rxjs/operators';
import {
  trigger,
  transition,
  style,
  animate,
  query,
  stagger,
} from '@angular/animations';

interface PaymentMethod {
  id: string;
  name: string;
  icon: string;
  description: string;
}

@Component({
  selector: 'app-checkout',
  standalone: true,
  imports: [
    CommonModule,
    RouterModule,
    MaterialModule,
    FormsModule,
    ReactiveFormsModule,
  ],
  templateUrl: './checkout.component.html',
  styleUrl: './checkout.component.scss',
  animations: [
    trigger('itemAnimation', [
      transition('* => *', [
        query(
          ':enter',
          [
            style({ opacity: 0, transform: 'translateX(-20px)' }),
            stagger(100, [
              animate(
                '0.3s ease',
                style({ opacity: 1, transform: 'translateX(0)' })
              ),
            ]),
          ],
          { optional: true }
        ),
        query(
          ':leave',
          [
            animate(
              '0.3s ease',
              style({ opacity: 0, transform: 'translateX(-20px)' })
            ),
          ],
          { optional: true }
        ),
      ]),
    ]),
  ],
})
export class CheckoutComponent implements OnInit {
  cartItems$!: Observable<CartItem[]>;
  cartTotal$!: Observable<number>;
  shippingCost = 0;
  isProcessing = false;
  isPaymentRedirecting = false;
  orderSummary$!: Observable<{
    subtotal: number;
    shipping: number;
    tax: number;
    total: number;
  }>;

  checkoutForm: FormGroup;

  paymentMethods: PaymentMethod[] = [
    {
      id: 'credit-card',
      name: 'Credit / Debit Card',
      icon: 'credit_card',
      description: 'Visa, Mastercard, American Express',
    },
    {
      id: 'upi',
      name: 'UPI',
      icon: 'currency_rupee',
      description: 'Google Pay, PhonePe, Paytm',
    },
    {
      id: 'paypal',
      name: 'PayPal',
      icon: 'account_balance_wallet',
      description: 'Fast and secure checkout',
    },
    {
      id: 'net-banking',
      name: 'Net Banking',
      icon: 'account_balance',
      description: 'All major banks supported',
    },
    {
      id: 'emi',
      name: 'EMI',
      icon: 'calendar_month',
      description: 'No-cost EMI available',
    },
    {
      id: 'cod',
      name: 'Cash on Delivery',
      icon: 'local_shipping',
      description: 'Pay when you receive',
    },
  ];

  constructor(private cartService: CartService, private fb: FormBuilder) {
    this.checkoutForm = this.fb.group({
      personalInfo: this.fb.group({
        email: ['', [Validators.required, Validators.email]],
        phone: ['', [Validators.required, Validators.pattern(/^\+?[\d\s-]+$/)]],
      }),
      shippingAddress: this.fb.group({
        fullName: ['', Validators.required],
        addressLine1: ['', Validators.required],
        addressLine2: [''],
        city: ['', Validators.required],
        state: ['', Validators.required],
        zipCode: [
          '',
          [Validators.required, Validators.pattern(/^\d{5}(-\d{4})?$/)],
        ],
        country: ['US', Validators.required],
      }),
      paymentMethod: ['credit-card', Validators.required],
      saveInfo: [false],
    });
  }

  ngOnInit(): void {
    this.cartItems$ = this.cartService.getCartItems();
    this.cartTotal$ = this.cartService.getCartTotal();

    // Calculate order summary
    this.orderSummary$ = combineLatest([this.cartTotal$, this.cartItems$]).pipe(
      map(([subtotal, items]) => {
        const shipping = items.length > 0 ? this.calculateShipping(items) : 0;
        const tax = subtotal * 0.08; // 8% tax rate
        return {
          subtotal,
          shipping,
          tax,
          total: subtotal + shipping + tax,
        };
      })
    );

    // Load saved info if available
    this.loadSavedInfo();
  }

  private calculateShipping(items: CartItem[]): number {
    const baseRate = 5.99;
    const itemCount = items.reduce((total, item) => total + item.quantity, 0);
    return itemCount > 5 ? baseRate + (itemCount - 5) * 0.5 : baseRate;
  }

  private loadSavedInfo(): void {
    const savedInfo = localStorage.getItem('checkoutInfo');
    if (savedInfo) {
      const info = JSON.parse(savedInfo);
      this.checkoutForm.patchValue({
        personalInfo: info.personalInfo,
        shippingAddress: info.shippingAddress,
      });
    }
  }

  updateQuantity(productId: number, quantity: number): void {
    if (quantity >= 0) {
      this.cartService.updateQuantity(productId, quantity);
    }
  }

  removeItem(productId: number): void {
    this.cartService.removeFromCart(productId);
  }

  getFormError(path: string, error: string): boolean {
    const control = this.checkoutForm.get(path);
    return control?.errors?.[error] && control.touched;
  }

  async checkout(): Promise<void> {
    if (this.checkoutForm.invalid) {
      this.markFormGroupTouched(this.checkoutForm);
      return;
    }

    this.isProcessing = true;

    if (this.checkoutForm.value.saveInfo) {
      localStorage.setItem(
        'checkoutInfo',
        JSON.stringify({
          personalInfo: this.checkoutForm.value.personalInfo,
          shippingAddress: this.checkoutForm.value.shippingAddress,
        })
      );
    }

    // Simulate checkout process
    setTimeout(() => {
      this.isProcessing = false;
      this.isPaymentRedirecting = true;

      // Simulate redirect to payment gateway
      setTimeout(() => {
        // In a real app, this would redirect to an actual payment gateway
        if (typeof window !== 'undefined') {
          window.location.href = '/payment-success';
        }
      }, 2000);
    }, 1500);
  }

  private markFormGroupTouched(formGroup: FormGroup): void {
    Object.values(formGroup.controls).forEach((control) => {
      control.markAsTouched();

      if (control instanceof FormGroup) {
        this.markFormGroupTouched(control);
      }
    });
  }
}
