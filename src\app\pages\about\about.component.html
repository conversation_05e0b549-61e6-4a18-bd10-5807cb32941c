<app-header></app-header>

<div class="about-page">
  <!-- Hero Section -->
  <section class="hero-section bg-primary text-white py-20">
    <div class="container mx-auto px-4 text-center">
      <h1 class="text-4xl md:text-5xl font-bold mb-4">About Headphone Zone</h1>
      <p class="text-xl max-w-3xl mx-auto">India's first and largest headphone store, dedicated to helping you find the perfect pair of headphones.</p>
    </div>
  </section>
  
  <!-- Our Story Section -->
  <section class="py-16 bg-white">
    <div class="container mx-auto px-4">
      <div class="grid grid-cols-1 md:grid-cols-2 gap-12 items-center">
        <div>
          <h2 class="text-3xl font-bold mb-6">Our Story</h2>
          <div class="space-y-4">
            <p>Founded in 2011 by <PERSON><PERSON><PERSON>, Headphone Zone was born out of a simple realization - India deserved better headphones.</p>
            <p>What started as a small online store has grown into India's largest specialized headphone retailer, offering a curated selection of the world's finest headphones, earphones, and audio accessories.</p>
            <p>Our mission is simple: to bring the world's best headphones to India and help people rediscover the joy of music through exceptional audio experiences.</p>
            <p>We believe that everyone deserves to hear their favorite music the way it was meant to be heard - with all its detail, emotion, and power intact.</p>
          </div>
        </div>
        <div class="relative">
          <img src="https://cdn.shopify.com/s/files/1/0153/8863/files/About-Us-Image_800x.jpg?v=1712903400" alt="Headphone Zone Store" class="rounded-lg shadow-lg">
          <div class="absolute -bottom-6 -left-6 bg-primary text-white p-4 rounded-lg shadow-lg">
            <p class="text-2xl font-bold">10+</p>
            <p class="text-sm">Years of Excellence</p>
          </div>
        </div>
      </div>
    </div>
  </section>
  
  <!-- Our Values Section -->
  <section class="py-16 bg-gray-50">
    <div class="container mx-auto px-4">
      <h2 class="text-3xl font-bold text-center mb-12">Our Values</h2>
      
      <div class="grid grid-cols-1 md:grid-cols-3 gap-8">
        <div class="bg-white p-8 rounded-lg shadow-md text-center">
          <div class="bg-primary bg-opacity-10 w-16 h-16 rounded-full flex items-center justify-center mx-auto mb-4">
            <mat-icon class="text-primary text-3xl">headphones</mat-icon>
          </div>
          <h3 class="text-xl font-semibold mb-3">Audiophile Quality</h3>
          <p class="text-gray-600">We only sell products that meet our strict standards for sound quality, build quality, and value.</p>
        </div>
        
        <div class="bg-white p-8 rounded-lg shadow-md text-center">
          <div class="bg-primary bg-opacity-10 w-16 h-16 rounded-full flex items-center justify-center mx-auto mb-4">
            <mat-icon class="text-primary text-3xl">school</mat-icon>
          </div>
          <h3 class="text-xl font-semibold mb-3">Education First</h3>
          <p class="text-gray-600">We believe in educating our customers about audio technology to help them make informed decisions.</p>
        </div>
        
        <div class="bg-white p-8 rounded-lg shadow-md text-center">
          <div class="bg-primary bg-opacity-10 w-16 h-16 rounded-full flex items-center justify-center mx-auto mb-4">
            <mat-icon class="text-primary text-3xl">people</mat-icon>
          </div>
          <h3 class="text-xl font-semibold mb-3">Community Building</h3>
          <p class="text-gray-600">We're building India's largest community of headphone enthusiasts through events, content, and shared experiences.</p>
        </div>
      </div>
    </div>
  </section>
  
  <!-- Our Journey Section -->
  <section class="py-16 bg-white">
    <div class="container mx-auto px-4">
      <h2 class="text-3xl font-bold text-center mb-12">Our Journey</h2>
      
      <div class="relative">
        <!-- Timeline Line -->
        <div class="absolute left-1/2 transform -translate-x-1/2 h-full w-1 bg-primary"></div>
        
        <!-- Timeline Items -->
        <div class="space-y-12">
          <div *ngFor="let milestone of milestones; let i = even" class="relative z-10">
            <div class="flex items-center justify-center mb-4">
              <div class="bg-primary text-white w-12 h-12 rounded-full flex items-center justify-center font-bold">
                {{ milestone.year }}
              </div>
            </div>
            
            <div [class]="i ? 'md:ml-auto md:mr-0' : 'md:mr-auto md:ml-0'" class="bg-white p-6 rounded-lg shadow-md max-w-md mx-auto md:mx-0">
              <h3 class="text-xl font-semibold mb-2">{{ milestone.title }}</h3>
              <p class="text-gray-600">{{ milestone.description }}</p>
            </div>
          </div>
        </div>
      </div>
    </div>
  </section>
  
  <!-- Team Section -->
  <section class="py-16 bg-gray-50">
    <div class="container mx-auto px-4">
      <h2 class="text-3xl font-bold text-center mb-12">Meet Our Team</h2>
      
      <div class="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-4 gap-8">
        <div *ngFor="let member of teamMembers" class="bg-white rounded-lg overflow-hidden shadow-md">
          <img [src]="member.image" [alt]="member.name" class="w-full aspect-square object-cover">
          <div class="p-6">
            <h3 class="text-xl font-semibold mb-1">{{ member.name }}</h3>
            <p class="text-primary font-medium mb-3">{{ member.position }}</p>
            <p class="text-gray-600 text-sm">{{ member.bio }}</p>
          </div>
        </div>
      </div>
    </div>
  </section>
  
  <!-- Newsletter Section -->
  <app-newsletter></app-newsletter>
</div>

<app-footer></app-footer>
