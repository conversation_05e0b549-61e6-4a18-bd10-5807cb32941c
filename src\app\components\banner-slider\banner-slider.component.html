<section class="banner-slider relative overflow-hidden" role="region" aria-label="Featured promotions" tabindex="0" (mousemove)="updateCursorPosition($event)" (mouseenter)="showCustomCursor()" (mouseleave)="hideCustomCursor()">
  <!-- Custom Cursor with Timer -->
  <div class="custom-cursor" [class.active]="isCustomCursorVisible" [style.left.px]="cursorX" [style.top.px]="cursorY">
    <svg class="cursor-timer" width="40" height="40" viewBox="0 0 40 40">
      <circle class="cursor-timer-bg" cx="20" cy="20" r="18" fill="none" stroke="#ffffff" stroke-width="2"></circle>
      <circle class="cursor-timer-progress" cx="20" cy="20" r="18" fill="none" stroke="#ff5500" stroke-width="2"
        [style.strokeDashoffset.px]="timerProgress"></circle>
    </svg>
  </div>

  <div class="banner-container relative h-[300px] sm:h-[400px] md:h-[500px]">
    <!-- Banner Slides -->
    <div *ngFor="let banner of banners; let i = index" class="banner-slide absolute top-0 left-0 w-full h-full"
      [@slideAnimation]="currentIndex$ | async" [class.opacity-100]="(currentIndex$ | async) === i"
      [class.opacity-0]="(currentIndex$ | async) !== i" [attr.aria-hidden]="(currentIndex$ | async) !== i"
      [style.z-index]="(currentIndex$ | async) === i ? 10 : 0">
      <div class="banner-image absolute inset-0">
        <img [src]="banner.imageUrl" [alt]="banner.title" class="w-full h-full object-cover">
      </div>

      <div class="container mx-auto h-full flex items-center relative z-10">
        <div class="banner-content text-white p-6 md:p-12 max-w-lg" [attr.aria-live]="'polite'">
          <h2 class="text-3xl md:text-4xl lg:text-5xl font-bold mb-2">{{ banner.title }}</h2>
          <p class="text-lg md:text-xl mb-6">{{ banner.subtitle }}</p>
          <a [routerLink]="banner.buttonLink" class="shop-now-button bg-orange-600 text-white px-6 py-3 inline-flex items-center font-medium uppercase tracking-wider"
            [attr.aria-label]="banner.buttonText + ' - ' + banner.title">
            {{ banner.buttonText }}
            <span class="ml-2">→</span>
          </a>
        </div>
      </div>
    </div>

    <!-- Navigation Controls -->
    <div class="absolute inset-x-0 bottom-0 top-0 flex items-center justify-between p-4">
      <!-- Previous Button -->
      <button (click)="prevBanner()"
        class="nav-button left-2 md:left-4 bg-black bg-opacity-30 hover:bg-opacity-50 rounded-full p-1 md:p-2 transition-all focus:outline-none focus:ring-2 focus:ring-primary"
        aria-label="Previous slide">
        <mat-icon class="text-white">chevron_left</mat-icon>
      </button>

      <!-- Next Button -->
      <button (click)="nextBanner()"
        class="nav-button right-2 md:right-4 bg-black bg-opacity-30 hover:bg-opacity-50 rounded-full p-1 md:p-2 transition-all focus:outline-none focus:ring-2 focus:ring-primary"
        aria-label="Next slide">
        <mat-icon class="text-white">chevron_right</mat-icon>
      </button>
    </div>

    <!-- Dots Indicator -->
    <div class="absolute bottom-4 left-0 right-0 flex justify-center items-center space-x-3 z-20">
      <!-- Dots -->
      <div class="flex space-x-2" role="tablist" aria-label="Slide indicators">
        <button *ngFor="let banner of banners; let i = index" (click)="goToBanner(i)"
          class="w-2 h-2 md:w-3 md:h-3 rounded-full transition-all focus:outline-none focus:ring-2 focus:ring-primary"
          [class.bg-primary]="(currentIndex$ | async) === i" [class.bg-white]="(currentIndex$ | async) !== i"
          [attr.aria-label]="'Go to slide ' + (i + 1)" [attr.aria-selected]="(currentIndex$ | async) === i"
          role="tab"></button>
      </div>
    </div>
  </div>
</section>