import { Injectable } from '@angular/core';
import { BehaviorSubject, Observable, of, throwError } from 'rxjs';
import { tap } from 'rxjs/operators';

export interface User {
  id: number;
  firstName: string;
  lastName: string;
  email: string;
  avatar?: string;
}

@Injectable({
  providedIn: 'root'
})
export class AuthService {
  private currentUserSubject = new BehaviorSubject<User | null>(null);
  public currentUser$ = this.currentUserSubject.asObservable();
  
  private isAuthenticatedSubject = new BehaviorSubject<boolean>(false);
  public isAuthenticated$ = this.isAuthenticatedSubject.asObservable();

  constructor() {
    // Check if user is already logged in from localStorage
    const storedUser = localStorage.getItem('currentUser');
    if (storedUser) {
      const user = JSON.parse(storedUser);
      this.currentUserSubject.next(user);
      this.isAuthenticatedSubject.next(true);
    }
  }

  login(email: string, password: string): Observable<User> {
    // In a real app, this would be an HTTP request to your backend
    if (email === '<EMAIL>' && password === 'password') {
      const user: User = {
        id: 1,
        firstName: 'Demo',
        lastName: 'User',
        email: '<EMAIL>',
        avatar: 'assets/images/avatars/avatar-1.jpg'
      };
      
      // Store user details in localStorage
      localStorage.setItem('currentUser', JSON.stringify(user));
      
      // Update subjects
      this.currentUserSubject.next(user);
      this.isAuthenticatedSubject.next(true);
      
      return of(user);
    }
    
    return throwError(() => 'Invalid email or password');
  }

  register(firstName: string, lastName: string, email: string, password: string): Observable<User> {
    // In a real app, this would be an HTTP request to your backend
    const user: User = {
      id: 1,
      firstName,
      lastName,
      email
    };
    
    // For demo purposes, we'll just return the user
    return of(user);
  }

  logout(): void {
    // Remove user from localStorage
    localStorage.removeItem('currentUser');
    
    // Update subjects
    this.currentUserSubject.next(null);
    this.isAuthenticatedSubject.next(false);
  }

  getCurrentUser(): User | null {
    return this.currentUserSubject.value;
  }

  isAuthenticated(): boolean {
    return this.isAuthenticatedSubject.value;
  }
}
