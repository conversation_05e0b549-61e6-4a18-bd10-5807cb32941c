import { Component, OnInit } from '@angular/core';
import { CommonModule } from '@angular/common';
import { ActivatedRoute, RouterModule } from '@angular/router';
import { MaterialModule } from '../../material/material.module';
import { FormsModule, ReactiveFormsModule, FormControl } from '@angular/forms';
import { HeaderComponent } from '../../components/header/header.component';
import { FooterComponent } from '../../components/footer/footer.component';
import { ProductService } from '../../services/product.service';
import { CartService } from '../../services/cart.service';
import { ToastService } from '../../services/toast.service';
import { Product } from '../../models/product.model';
import { Observable, of, map, switchMap, tap } from 'rxjs';
import { ProductBadgeComponent } from '../../components/product-badge/product-badge.component';
import { SoundSignatureIndicatorComponent } from '../../components/sound-signature-indicator/sound-signature-indicator.component';

@Component({
  selector: 'app-product-detail',
  standalone: true,
  imports: [
    CommonModule,
    RouterModule,
    MaterialModule,
    FormsModule,
    ReactiveFormsModule,
    HeaderComponent,
    FooterComponent,
    ProductBadgeComponent,
    SoundSignatureIndicatorComponent
  ],
  templateUrl: './product-detail.component.html',
  styleUrls: ['./product-detail.component.scss']
})
export class ProductDetailComponent implements OnInit {
  product$!: Observable<Product | undefined>;
  relatedProducts$!: Observable<Product[]>;
  quantityControl = new FormControl(1, { nonNullable: true });
  selectedTab = 0;

  // Product specifications
  specifications = [
    { name: 'Driver Type', value: 'Dynamic' },
    { name: 'Driver Size', value: '10mm' },
    { name: 'Frequency Response', value: '20Hz - 20kHz' },
    { name: 'Impedance', value: '32 Ohms' },
    { name: 'Sensitivity', value: '102dB' },
    { name: 'Cable Length', value: '1.2m' },
    { name: 'Connector', value: '3.5mm Gold Plated' },
    { name: 'Weight', value: '8g (without cable)' }
  ];

  // Product reviews
  reviews = [
    {
      author: 'Rahul S.',
      rating: 5,
      date: '2023-12-15',
      title: 'Exceptional sound quality',
      content: 'These are the best IEMs I\'ve ever owned. The sound stage is incredible and the bass response is perfect. Highly recommended for any audiophile.'
    },
    {
      author: 'Priya M.',
      rating: 4,
      date: '2023-11-20',
      title: 'Great value for money',
      content: 'For the price, these offer excellent sound quality. The mids are clear and the highs are crisp. The only downside is the cable quality could be better.'
    },
    {
      author: 'Amit K.',
      rating: 5,
      date: '2023-10-05',
      title: 'Perfect for daily use',
      content: 'I use these every day for my commute and they\'re perfect. Comfortable for long listening sessions and the sound isolation is great.'
    }
  ];

  constructor(
    private route: ActivatedRoute,
    private productService: ProductService,
    private cartService: CartService,
    private toastService: ToastService
  ) {}

  ngOnInit(): void {
    this.product$ = this.route.paramMap.pipe(
      map(params => Number(params.get('id'))),
      switchMap(id => this.productService.getProductById(id)),
      tap(product => {
        if (product) {
          // Update specifications from product if available
          if (product.technicalSpecs) {
            this.specifications = Object.entries(product.technicalSpecs)
              .filter(([_, value]) => value) // Filter out undefined values
              .map(([key, value]) => {
                // Convert camelCase to Title Case with spaces
                const name = key.replace(/([A-Z])/g, ' $1')
                  .replace(/^./, str => str.toUpperCase());
                return { name, value: value as string };
              });
          }

          this.relatedProducts$ = this.productService.getRelatedProducts(product.category, product.id);
        } else {
          this.relatedProducts$ = of([]);
        }
      })
    );
  }

  addToCart(product: Product): void {
    const quantity = this.quantityControl.value;
    this.cartService.addToCart(product, quantity);
    this.toastService.showSuccess(`${quantity} x ${product.name} added to cart`);
  }

  incrementQuantity(): void {
    this.quantityControl.setValue(this.quantityControl.value + 1);
  }

  decrementQuantity(): void {
    if (this.quantityControl.value > 1) {
      this.quantityControl.setValue(this.quantityControl.value - 1);
    }
  }

  getDiscountPercentage(product: Product): number {
    if (!product.discountedPrice) return 0;
    return Math.round(((product.price - product.discountedPrice) / product.price) * 100);
  }

  getStockStatus(stock: number): 'in-stock' | 'low-stock' | 'out-of-stock' {
    if (stock === 0) return 'out-of-stock';
    if (stock <= 5) return 'low-stock';
    return 'in-stock';
  }
}
