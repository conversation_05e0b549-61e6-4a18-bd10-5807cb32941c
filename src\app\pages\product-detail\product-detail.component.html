<app-header></app-header>

<div class="container mx-auto px-4 py-8">
  <ng-container *ngIf="product$ | async as product; else loading">
    <!-- Breadcrumb -->
    <div class="breadcrumb mb-6">
      <ul class="flex items-center text-sm">
        <li class="flex items-center">
          <a routerLink="/" class="text-gray-500 hover:text-primary">Home</a>
          <span class="mx-2 text-gray-400">/</span>
        </li>
        <li class="flex items-center">
          <a [routerLink]="['/collections', product.category.toLowerCase()]" class="text-gray-500 hover:text-primary">{{ product.category }}</a>
          <span class="mx-2 text-gray-400">/</span>
        </li>
        <li class="text-primary font-medium">{{ product.name }}</li>
      </ul>
    </div>

    <!-- Product Details -->
    <div class="grid grid-cols-1 md:grid-cols-2 gap-8 mb-12">
      <!-- Product Images -->
      <div class="product-images">
        <div class="main-image bg-gray-50 rounded-lg overflow-hidden mb-4">
          <img [src]="product.imageUrl" [alt]="product.name" class="w-full h-auto object-contain">
        </div>

        <!-- Thumbnail Images (Placeholder) -->
        <div class="thumbnails grid grid-cols-5 gap-2">
          <div *ngFor="let i of [1, 2, 3, 4, 5]" class="thumbnail cursor-pointer border border-gray-200 rounded overflow-hidden" [class.border-primary]="i === 1">
            <img [src]="product.imageUrl" [alt]="product.name" class="w-full h-auto object-cover">
          </div>
        </div>
      </div>

      <!-- Product Info -->
      <div class="product-info">
        <!-- Product Title and Badges -->
        <div class="mb-4">
          <div class="flex items-center mb-2">
            <span *ngIf="product.isNew" class="bg-secondary text-white text-xs font-medium px-2 py-1 rounded mr-2">NEW</span>
            <span *ngIf="product.isHot" class="bg-primary text-white text-xs font-medium px-2 py-1 rounded mr-2">HOT</span>
            <span *ngIf="product.discountedPrice" class="bg-green-500 text-white text-xs font-medium px-2 py-1 rounded">{{ getDiscountPercentage(product) }}% OFF</span>
          </div>
          <h1 class="text-2xl md:text-3xl font-semibold text-gray-900">{{ product.name }}</h1>

          <!-- Product Badges -->
          <div *ngIf="product.badges && product.badges.length > 0" class="flex flex-wrap mt-3">
            <app-product-badge
              *ngFor="let badge of product.badges"
              [badge]="badge"
              size="medium">
            </app-product-badge>
          </div>
        </div>

        <!-- Ratings -->
        <div class="flex items-center mb-4">
          <div class="flex">
            <mat-icon *ngFor="let i of [1, 2, 3, 4, 5]" class="text-amber-400">
              {{ i <= product.rating ? 'star' : (i - 0.5 <= product.rating ? 'star_half' : 'star_border') }}
            </mat-icon>
          </div>
          <span class="ml-2 text-sm text-gray-600">{{ product.reviewCount }} reviews</span>
        </div>

        <!-- Price -->
        <div class="mb-6">
          <div class="flex items-baseline">
            <span *ngIf="product.discountedPrice" class="text-gray-400 line-through text-lg mr-2">
              ₹{{ product.price.toFixed(2) }}
            </span>
            <span class="text-2xl font-bold text-primary">
              ₹{{ (product.discountedPrice || product.price).toFixed(2) }}
            </span>
            <span *ngIf="product.discountedPrice" class="ml-2 text-green-600 text-sm">
              Save ₹{{ (product.price - product.discountedPrice).toFixed(2) }}
            </span>
          </div>
          <p class="text-sm text-gray-500 mt-1">Inclusive of all taxes</p>
        </div>

        <!-- Stock Status -->
        <div class="mb-6">
          <div [ngSwitch]="getStockStatus(product.stock)" class="text-sm font-medium">
            <div *ngSwitchCase="'out-of-stock'" class="text-error flex items-center">
              <mat-icon class="mr-1">cancel</mat-icon>
              <span>Out of Stock</span>
            </div>
            <div *ngSwitchCase="'low-stock'" class="text-error flex items-center">
              <mat-icon class="mr-1">error_outline</mat-icon>
              <span>Only {{ product.stock }} left!</span>
            </div>
            <div *ngSwitchCase="'in-stock'" class="text-success flex items-center">
              <mat-icon class="mr-1">check_circle</mat-icon>
              <span>In Stock</span>
            </div>
          </div>
        </div>

        <!-- Description -->
        <div class="mb-6">
          <p class="text-gray-700">{{ product.description }}</p>
        </div>

        <!-- Sound Signature -->
        <div *ngIf="product.soundSignature" class="mb-6">
          <h3 class="font-medium mb-3">Sound Signature</h3>
          <app-sound-signature-indicator
            [signature]="product.soundSignature"
            size="medium"
            [showLabel]="true">
          </app-sound-signature-indicator>
        </div>

        <!-- Quantity and Add to Cart -->
        <div class="mb-6">
          <label class="block text-sm font-medium text-gray-700 mb-2">Quantity</label>
          <div class="flex items-center">
            <button mat-icon-button color="primary" (click)="decrementQuantity()" [disabled]="quantityControl.value <= 1">
              <mat-icon>remove</mat-icon>
            </button>
            <input [formControl]="quantityControl" type="number" min="1" max="99" class="w-16 text-center border border-gray-300 rounded mx-2 py-2">
            <button mat-icon-button color="primary" (click)="incrementQuantity()">
              <mat-icon>add</mat-icon>
            </button>
          </div>
        </div>

        <!-- Action Buttons -->
        <div class="flex flex-col sm:flex-row gap-4 mb-8">
          <button mat-raised-button color="primary" class="flex-1 py-3" [disabled]="product.stock === 0" (click)="addToCart(product)">
            <mat-icon class="mr-2">shopping_cart</mat-icon>
            ADD TO CART
          </button>
          <button mat-stroked-button color="primary" class="flex-1 py-3">
            <mat-icon>favorite_border</mat-icon>
            ADD TO WISHLIST
          </button>
        </div>

        <!-- Delivery Info -->
        <div class="border border-gray-200 rounded-lg p-4 mb-6">
          <h3 class="font-medium mb-3">Delivery Information</h3>
          <div class="flex items-start mb-2">
            <mat-icon class="text-primary mr-2">local_shipping</mat-icon>
            <div>
              <p class="text-sm font-medium">Free shipping on orders above ₹1,500</p>
              <p class="text-xs text-gray-500">Delivered in 1-3 business days</p>
            </div>
          </div>
          <div class="flex items-start">
            <mat-icon class="text-primary mr-2">replay</mat-icon>
            <div>
              <p class="text-sm font-medium">7-day easy returns</p>
              <p class="text-xs text-gray-500">Change of mind? No problem!</p>
            </div>
          </div>
        </div>

        <!-- Share -->
        <div>
          <h3 class="font-medium mb-2">Share this product</h3>
          <div class="flex space-x-3">
            <a href="#" class="w-8 h-8 rounded-full bg-gray-200 flex items-center justify-center hover:bg-primary hover:text-white transition-colors">
              <mat-icon class="text-sm">facebook</mat-icon>
            </a>
            <a href="#" class="w-8 h-8 rounded-full bg-gray-200 flex items-center justify-center hover:bg-primary hover:text-white transition-colors">
              <mat-icon class="text-sm">photo_camera</mat-icon>
            </a>
            <a href="#" class="w-8 h-8 rounded-full bg-gray-200 flex items-center justify-center hover:bg-primary hover:text-white transition-colors">
              <mat-icon class="text-sm">alternate_email</mat-icon>
            </a>
            <a href="#" class="w-8 h-8 rounded-full bg-gray-200 flex items-center justify-center hover:bg-primary hover:text-white transition-colors">
              <mat-icon class="text-sm">link</mat-icon>
            </a>
          </div>
        </div>
      </div>
    </div>

    <!-- Product Tabs -->
    <div class="mb-12">
      <mat-tab-group [(selectedIndex)]="selectedTab" animationDuration="300ms" mat-align-tabs="start">
        <mat-tab label="Description">
          <div class="py-6">
            <h2 class="text-xl font-semibold mb-4">Product Description</h2>
            <div class="prose max-w-none">
              <p>{{ product.description }}</p>
              <p class="mt-4">Experience audio like never before with the {{ product.name }}. Designed for audiophiles who demand the best, these premium headphones deliver exceptional sound quality with deep bass, clear mids, and crisp highs.</p>
              <p class="mt-4">The comfortable design ensures you can enjoy your music for hours without fatigue, while the durable construction means these headphones will be your trusted companion for years to come.</p>
            </div>
          </div>
        </mat-tab>

        <mat-tab label="Specifications">
          <div class="py-6">
            <h2 class="text-xl font-semibold mb-4">Technical Specifications</h2>
            <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
              <div *ngFor="let spec of specifications" class="flex border-b border-gray-200 py-2">
                <span class="font-medium w-1/2">{{ spec.name }}</span>
                <span class="text-gray-700 w-1/2">{{ spec.value }}</span>
              </div>
            </div>
          </div>
        </mat-tab>

        <mat-tab label="Reviews">
          <div class="py-6">
            <div class="flex items-center justify-between mb-6">
              <h2 class="text-xl font-semibold">Customer Reviews</h2>
              <button mat-raised-button color="primary">Write a Review</button>
            </div>

            <!-- Reviews List -->
            <div class="space-y-6">
              <div *ngFor="let review of reviews" class="border-b border-gray-200 pb-6">
                <div class="flex justify-between items-start mb-2">
                  <div>
                    <h3 class="font-medium">{{ review.title }}</h3>
                    <div class="flex items-center mt-1">
                      <div class="flex">
                        <mat-icon *ngFor="let i of [1, 2, 3, 4, 5]" class="text-amber-400 text-sm">
                          {{ i <= review.rating ? 'star' : 'star_border' }}
                        </mat-icon>
                      </div>
                      <span class="ml-2 text-xs text-gray-500">{{ review.date | date }}</span>
                    </div>
                  </div>
                  <span class="text-sm font-medium">{{ review.author }}</span>
                </div>
                <p class="text-gray-700 text-sm mt-2">{{ review.content }}</p>
              </div>
            </div>
          </div>
        </mat-tab>
      </mat-tab-group>
    </div>

    <!-- Related Products -->
    <div class="mb-12">
      <h2 class="text-2xl font-semibold mb-6">You May Also Like</h2>
      <div class="grid grid-cols-1 sm:grid-cols-2 md:grid-cols-3 lg:grid-cols-4 gap-6">
        <div *ngFor="let relatedProduct of relatedProducts$ | async" class="product-card">
          <!-- Discount Badge -->
          <div *ngIf="relatedProduct.discountedPrice" class="absolute top-2 left-2 z-10">
            <div class="bg-primary text-white text-xs font-medium px-2 py-1 rounded product-badge">
              {{ getDiscountPercentage(relatedProduct) }}% OFF
            </div>
          </div>

          <!-- Product Image -->
          <div class="product-image relative">
            <a [routerLink]="['/product', relatedProduct.id]">
              <img [src]="relatedProduct.imageUrl" [alt]="relatedProduct.name" class="w-full h-auto object-contain">
            </a>

            <!-- Product Badges -->
            <div *ngIf="relatedProduct.badges && relatedProduct.badges.length > 0" class="absolute bottom-2 left-2 z-10 flex flex-wrap">
              <app-product-badge
                *ngFor="let badge of relatedProduct.badges"
                [badge]="badge"
                size="small">
              </app-product-badge>
            </div>
          </div>

          <!-- Product Info -->
          <div class="product-info">
            <h3 class="product-title">
              <a [routerLink]="['/product', relatedProduct.id]">{{ relatedProduct.name }}</a>
            </h3>

            <!-- Rating -->
            <div class="flex items-center mb-2">
              <div class="flex">
                <mat-icon *ngFor="let i of [1, 2, 3, 4, 5]" class="text-amber-400 text-sm">
                  {{ i <= relatedProduct.rating ? 'star' : (i - 0.5 <= relatedProduct.rating ? 'star_half' : 'star_border') }}
                </mat-icon>
              </div>
              <span class="text-xs text-gray-500 ml-1">({{ relatedProduct.reviewCount }})</span>
            </div>

            <!-- Price -->
            <div class="product-price">
              <span *ngIf="relatedProduct.discountedPrice" class="original-price">
                ₹{{ relatedProduct.price.toFixed(2) }}
              </span>
              <span class="current-price">
                ₹{{ (relatedProduct.discountedPrice || relatedProduct.price).toFixed(2) }}
              </span>
            </div>
          </div>
        </div>
      </div>
    </div>
  </ng-container>

  <!-- Loading State -->
  <ng-template #loading>
    <div class="flex justify-center items-center py-12">
      <mat-spinner diameter="40"></mat-spinner>
    </div>
  </ng-template>
</div>

<app-footer></app-footer>
