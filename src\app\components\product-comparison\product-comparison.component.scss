.section-title {
  position: relative;
  font-size: 1.75rem;
  font-weight: 600;
  color: var(--text-primary);
  padding-bottom: 0.5rem;
  
  &::after {
    content: '';
    position: absolute;
    bottom: 0;
    left: 50%;
    transform: translateX(-50%);
    width: 60px;
    height: 3px;
    background-color: var(--primary);
    border-radius: 3px;
  }
}

.comparison-table {
  border-collapse: separate;
  border-spacing: 0;
  border: 1px solid #e5e7eb;
  border-radius: 8px;
  overflow: hidden;
  
  th, td {
    padding: 1rem;
    text-align: left;
    vertical-align: middle;
    border-bottom: 1px solid #e5e7eb;
    
    &:not(:first-child) {
      border-left: 1px solid #e5e7eb;
    }
  }
  
  th {
    background-color: #f9fafb;
    font-weight: 600;
  }
  
  .sticky-col {
    position: sticky;
    left: 0;
    z-index: 1;
    background-color: #f9fafb;
    box-shadow: 2px 0 5px rgba(0, 0, 0, 0.05);
  }
  
  .product-col {
    min-width: 200px;
    max-width: 250px;
    
    .product-image {
      height: 120px;
      display: flex;
      align-items: center;
      justify-content: center;
      
      img {
        max-height: 100%;
        max-width: 100%;
        object-fit: contain;
      }
    }
  }
}

// Responsive styles
@media (max-width: 768px) {
  .comparison-table {
    display: block;
    overflow-x: auto;
    white-space: nowrap;
    
    th, td {
      min-width: 150px;
    }
    
    .sticky-col {
      min-width: 120px;
    }
  }
}

// Animation for adding/removing products
.product-col {
  transition: all 0.3s ease;
  animation: fadeIn 0.3s ease;
}

@keyframes fadeIn {
  from {
    opacity: 0;
    transform: translateY(10px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

// Highlight differences
.highlight {
  background-color: rgba(var(--primary-rgb), 0.05);
  font-weight: 500;
}

// Better focus styles
button:focus-visible {
  outline: none;
  box-shadow: 0 0 0 2px var(--primary), 0 0 0 4px white;
}

// Nested accordion in mobile menu
.nested-accordion {
  .mat-expansion-panel {
    box-shadow: none !important;
    
    .mat-expansion-panel-header {
      padding-left: 32px;
    }
    
    .mat-expansion-panel-body {
      padding-left: 32px;
    }
  }
}
