import { Component, OnInit, OnDestroy } from '@angular/core';
import { CommonModule } from '@angular/common';
import { RouterModule } from '@angular/router';
import { MaterialModule } from '../../material/material.module';
import { ProductService } from '../../services/product.service';
import { CartService } from '../../services/cart.service';
import { ToastService } from '../../services/toast.service';
import { Product } from '../../models/product.model';
import {
  Observable,
  Subscription,
  BehaviorSubject,
  interval,
  combineLatest,
} from 'rxjs';
import { map, takeUntil, share } from 'rxjs/operators';
import {
  trigger,
  transition,
  style,
  animate,
  stagger,
  query,
} from '@angular/animations';

interface CountdownTime {
  hours: number;
  minutes: number;
  seconds: number;
  progress: number;
}

@Component({
  selector: 'app-featured-deals',
  standalone: true,
  imports: [CommonModule, RouterModule, MaterialModule],
  templateUrl: './featured-deals.component.html',
  styleUrl: './featured-deals.component.scss',
  animations: [
    trigger('dealAnimation', [
      transition('* => *', [
        query(
          ':enter',
          [
            style({ opacity: 0, transform: 'translateY(20px)' }),
            stagger(100, [
              animate(
                '0.5s ease',
                style({ opacity: 1, transform: 'translateY(0)' })
              ),
            ]),
          ],
          { optional: true }
        ),
      ]),
    ]),
  ],
})
export class FeaturedDealsComponent implements OnInit, OnDestroy {
  featuredProducts$!: Observable<Product[]>;
  private countdownSubject = new BehaviorSubject<{
    [key: number]: CountdownTime;
  }>({});
  countdown$ = this.countdownSubject.asObservable();
  private destroy$ = new BehaviorSubject<void>(undefined);
  stockLevels: { [key: number]: BehaviorSubject<number> } = {};

  constructor(
    private productService: ProductService,
    private cartService: CartService,
    private toastService: ToastService
  ) {}

  ngOnInit(): void {
    this.initializeFeaturedProducts();
    this.startCountdownTimer();
  }

  ngOnDestroy(): void {
    this.destroy$.next();
    this.destroy$.complete();
    Object.values(this.stockLevels).forEach((subject) => subject.complete());
  }

  private initializeFeaturedProducts(): void {
    this.featuredProducts$ = this.productService.getFeaturedDeals().pipe(
      map((deals) => {
        deals.forEach((product) => {
          // Initialize countdown for each product
          const totalSeconds = Math.floor(Math.random() * 86400) + 3600;
          this.initializeCountdown(product.id, totalSeconds);

          // Initialize stock level tracking
          this.stockLevels[product.id] = new BehaviorSubject<number>(
            product.stock
          );
        });
        return deals;
      }),
      share()
    );
  }

  private startCountdownTimer(): void {
    interval(1000)
      .pipe(takeUntil(this.destroy$))
      .subscribe(() => {
        const currentCountdowns = this.countdownSubject.value;
        const updatedCountdowns: { [key: number]: CountdownTime } = {};

        Object.entries(currentCountdowns).forEach(([productId, time]) => {
          const totalSeconds =
            time.hours * 3600 + time.minutes * 60 + time.seconds;
          if (totalSeconds > 0) {
            const newTotalSeconds = totalSeconds - 1;
            const hours = Math.floor(newTotalSeconds / 3600);
            const minutes = Math.floor((newTotalSeconds % 3600) / 60);
            const seconds = newTotalSeconds % 60;
            const progress = 1 - newTotalSeconds / (24 * 3600);

            updatedCountdowns[Number(productId)] = {
              hours,
              minutes,
              seconds,
              progress,
            };
          } else {
            // Reset timer when it reaches zero (for demo purposes)
            const newTotalSeconds = Math.floor(Math.random() * 86400) + 3600;
            this.initializeCountdown(Number(productId), newTotalSeconds);
            updatedCountdowns[Number(productId)] =
              this.countdownSubject.value[Number(productId)];
          }
        });

        this.countdownSubject.next(updatedCountdowns);
      });
  }

  private initializeCountdown(productId: number, totalSeconds: number): void {
    const hours = Math.floor(totalSeconds / 3600);
    const minutes = Math.floor((totalSeconds % 3600) / 60);
    const seconds = totalSeconds % 60;
    const progress = 1 - totalSeconds / (24 * 3600);

    const currentCountdowns = this.countdownSubject.value;
    this.countdownSubject.next({
      ...currentCountdowns,
      [productId]: { hours, minutes, seconds, progress },
    });
  }

  formatTime(value: number): string {
    return value < 10 ? `0${value}` : `${value}`;
  }

  getDiscountPercentage(product: Product): number {
    if (!product.discountedPrice) return 0;
    return Math.round(
      ((product.price - product.discountedPrice) / product.price) * 100
    );
  }

  getRemainingStock(productId: number): Observable<number> {
    return this.stockLevels[productId].asObservable();
  }

  getStockWarningLevel(stock: number): 'low' | 'medium' | 'high' {
    if (stock <= 5) return 'low';
    if (stock <= 10) return 'medium';
    return 'high';
  }

  addToCart(product: Product): void {
    if (this.stockLevels[product.id].value > 0) {
      this.cartService.addToCart(product);
      this.toastService.showProductPurchaseNotification(product.name);
      this.stockLevels[product.id].next(this.stockLevels[product.id].value - 1);
    }
  }
}
