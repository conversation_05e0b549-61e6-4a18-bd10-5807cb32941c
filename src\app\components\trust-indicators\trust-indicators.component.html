<section class="trust-indicators py-12 bg-white border-t border-b border-gray-200">
  <div class="container mx-auto px-4">
    <div class="grid grid-cols-1 sm:grid-cols-2 md:grid-cols-3 lg:grid-cols-5 gap-6" [@staggerAnimation]="visiblePoints.length">
      <div *ngFor="let point of visiblePoints; let i = index"
        class="trust-point text-center transition-all duration-300"
        [class.opacity-0]="!point">
        <!-- Icon -->
        <div class="icon-wrapper mx-auto mb-3">
          <img *ngIf="point.image" [src]="point.image" [alt]="point.title" class="h-12 mx-auto">
          <mat-icon *ngIf="!point.image" class="text-primary text-3xl">{{ point.icon }}</mat-icon>
        </div>

        <!-- Title -->
        <h3 class="text-base font-semibold mb-1 text-gray-800">{{ point.title }}</h3>

        <!-- Description -->
        <p class="text-gray-600 text-xs mb-2">{{ point.description }}</p>

        <!-- Metric -->
        <div *ngIf="point.metric" class="text-xs font-medium text-primary">
          {{ point.metric }}
        </div>
      </div>
    </div>
  </div>
</section>