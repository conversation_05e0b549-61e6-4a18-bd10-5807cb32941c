.payment-success {
  background: linear-gradient(135deg, #f8f9fa 0%, #ffffff 100%);

  .success-checkmark {
    animation: checkmarkBounce 1s cubic-bezier(0.68, -0.55, 0.265, 1.55);

    mat-icon {
      color: var(--success);
      filter: drop-shadow(0 4px 6px rgba(0, 150, 0, 0.2));
    }
  }
}

// Success animation
@keyframes checkmarkBounce {
  0% {
    transform: scale(0);
    opacity: 0;
  }
  50% {
    transform: scale(1.2);
  }
  100% {
    transform: scale(1);
    opacity: 1;
  }
}

// Order details card
.bg-white {
  backdrop-filter: blur(10px);
  border: 1px solid rgba(255, 255, 255, 0.1);
  transition: transform 0.3s ease, box-shadow 0.3s ease;

  &:hover {
    transform: translateY(-2px);
    box-shadow: 0 8px 16px rgba(0, 0, 0, 0.1);
  }
}

// Next step cards
.next-step-card {
  transition: all 0.3s ease;
  border: 1px solid transparent;

  &:hover {
    transform: translateY(-4px);
    border-color: var(--primary);
    box-shadow: 0 12px 20px rgba(0, 0, 0, 0.1);

    mat-icon {
      transform: scale(1.1);
    }
  }

  mat-icon {
    transition: transform 0.3s ease;
  }
}

// Copy button
button[mat-icon-button] {
  opacity: 0.7;
  transition: all 0.2s ease;

  &:hover {
    opacity: 1;
    transform: scale(1.1);
  }
}

// Status indicators
.text-success {
  color: var(--success);

  mat-icon {
    filter: drop-shadow(0 2px 4px rgba(0, 150, 0, 0.2));
  }
}

// Return to home button
a[mat-raised-button] {
  transition: all 0.3s ease;
  
  &:hover {
    transform: translateY(-2px);
    box-shadow: 0 4px 8px rgba(var(--primary-rgb), 0.3);
  }
}

// Font styling
.font-mono {
  font-family: 'Roboto Mono', monospace;
  letter-spacing: 0.5px;
}

// Responsive adjustments
@media (max-width: 768px) {
  .success-checkmark mat-icon {
    font-size: 3rem;
  }

  .next-step-card {
    &:hover {
      transform: none;
    }
  }
}

// Grid layout refinements
.grid {
  gap: 2rem;
}

// Reduced motion preferences
@media (prefers-reduced-motion: reduce) {
  .success-checkmark,
  .bg-white,
  .next-step-card,
  button[mat-icon-button],
  a[mat-raised-button] {
    animation: none !important;
    transition: none !important;
  }
}

// Helper classes
.text-gray-600 {
  color: #666;
}

.shadow-md {
  box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
}

.space-y-3 > * + * {
  margin-top: 0.75rem;
}

// Print styles
@media print {
  .payment-success {
    background: none;
  }

  .next-step-card,
  a[mat-raised-button] {
    display: none;
  }

  .bg-white {
    box-shadow: none;
    border: 1px solid #ddd;
  }
}