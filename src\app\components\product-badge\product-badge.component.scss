.badge {
  display: inline-flex;
  align-items: center;
  border-radius: 4px;
  padding: 0.25rem 0.5rem;
  font-weight: 500;
  font-size: 0.75rem;
  line-height: 1;
  white-space: nowrap;
  margin-right: 0.5rem;
  margin-bottom: 0.5rem;
  box-shadow: 0 1px 2px rgba(0, 0, 0, 0.1);
  transition: all 0.2s ease;

  &:hover {
    transform: translateY(-1px);
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.15);
  }

  .badge-icon {
    font-size: 1rem;
    margin-right: 0.25rem;
  }

  &.small {
    font-size: 0.65rem;
    padding: 0.15rem 0.35rem;

    .badge-icon {
      font-size: 0.8rem;
    }
  }

  &.medium {
    font-size: 0.75rem;
    padding: 0.25rem 0.5rem;

    .badge-icon {
      font-size: 1rem;
    }
  }

  &.large {
    font-size: 0.85rem;
    padding: 0.35rem 0.7rem;

    .badge-icon {
      font-size: 1.2rem;
    }
  }

  // Badge colors
  &.green {
    background-color: #e6f7ed;
    color: #0d6832;
    border: 1px solid #a7e9c3;
  }

  &.blue {
    background-color: #e6f3ff;
    color: #0a558c;
    border: 1px solid #a7d8ff;
  }

  &.purple {
    background-color: #f2e6ff;
    color: #5a0ca7;
    border: 1px solid #d4a7ff;
  }

  &.gold {
    background-color: #fff8e6;
    color: #8c6c0a;
    border: 1px solid #ffe0a7;
  }

  &.red {
    background-color: #ffe6e6;
    color: #a70c0c;
    border: 1px solid #ffa7a7;
  }

  &.black {
    background-color: #333333;
    color: #ffffff;
    border: 1px solid #666666;
  }

  &.gray {
    background-color: #f2f2f2;
    color: #666666;
    border: 1px solid #dddddd;
  }
}