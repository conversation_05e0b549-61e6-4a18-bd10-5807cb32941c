<section class="sound-signature-guide py-12 bg-gray-50">
  <div class="container mx-auto px-4">
    <div class="text-center mb-8">
      <h2 class="section-title inline-block">Sound Signature Guide</h2>
      <p class="text-gray-600 mt-3 max-w-2xl mx-auto">Understand different sound profiles to find headphones that match your listening preferences.</p>
    </div>

    <!-- Sound Signature Selector -->
    <div class="signature-selector flex flex-wrap justify-center gap-3 mb-10">
      <button *ngFor="let signature of soundSignatures"
        class="signature-button"
        [class.active]="selectedSignature?.id === signature.id"
        (click)="selectSignature(signature)">
        <span class="icon text-2xl">{{ signature.icon }}</span>
        <span class="name">{{ signature.name }}</span>
      </button>
    </div>

    <!-- Selected Signature Details -->
    <div *ngIf="selectedSignature" class="signature-details bg-white rounded-lg shadow-md overflow-hidden">
      <div class="grid md:grid-cols-2 gap-0">
        <!-- Left Column: Description -->
        <div class="p-6 md:p-8 flex flex-col">
          <div class="flex items-center mb-4">
            <span class="text-4xl mr-3">{{ selectedSignature.icon }}</span>
            <h3 class="text-2xl font-bold">{{ selectedSignature.name }}</h3>
          </div>

          <p class="text-gray-700 mb-6">{{ selectedSignature.description }}</p>

          <div class="grid md:grid-cols-2 gap-6 mb-6">
            <div>
              <h4 class="font-semibold mb-3 text-gray-800">Characteristics</h4>
              <ul class="list-disc list-inside text-gray-700 space-y-1">
                <li *ngFor="let characteristic of selectedSignature.characteristics">{{ characteristic }}</li>
              </ul>
            </div>

            <div>
              <h4 class="font-semibold mb-3 text-gray-800">Best For Genres</h4>
              <div class="flex flex-wrap gap-2">
                <span *ngFor="let genre of selectedSignature.genres"
                  class="inline-block px-3 py-1 bg-gray-100 text-gray-700 rounded-full text-sm">
                  {{ genre }}
                </span>
              </div>
            </div>
          </div>

          <div class="mt-auto">
            <a mat-stroked-button color="primary" routerLink="/pages/sound-signature-guide" class="w-full">
              Learn More About Sound Signatures
            </a>
          </div>
        </div>

        <!-- Right Column: Example Products -->
        <div class="bg-gray-50 p-6 md:p-8">
          <h4 class="font-semibold mb-4 text-gray-800">Recommended Products with this Sound Signature</h4>

          <div class="grid gap-4">
            <div *ngFor="let example of selectedSignature.examples" class="product-card flex items-center p-3 bg-white rounded-lg shadow-sm hover:shadow-md transition-shadow">
              <a [routerLink]="example.path" class="product-image w-16 h-16 flex-shrink-0 mr-4">
                <img [src]="example.image" [alt]="example.name" class="w-full h-full object-contain">
              </a>

              <div class="flex-grow">
                <h5 class="font-medium text-gray-900">
                  <a [routerLink]="example.path" class="hover:text-primary">{{ example.name }}</a>
                </h5>
                <p class="text-sm text-gray-500">{{ selectedSignature.name }} Sound</p>
              </div>

              <a mat-icon-button color="primary" [routerLink]="example.path">
                <mat-icon>arrow_forward</mat-icon>
              </a>
            </div>
          </div>

          <div class="mt-6">
            <a mat-raised-button color="primary" [routerLink]="'/collections/' + selectedSignature.id + '-sound'" class="w-full">
              View All {{ selectedSignature.name }} Headphones
            </a>
          </div>
        </div>
      </div>
    </div>
  </div>
</section>
