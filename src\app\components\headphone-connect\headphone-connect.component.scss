.headphone-connect-section {
  background-color: #f9f9f9;
}

.event-card {
  transition: transform 0.3s ease, box-shadow 0.3s ease;

  &:hover {
    transform: translateY(-5px);
  }

  img {
    transition: transform 0.5s ease;
  }

  &:hover img {
    transform: scale(1.05);
  }
}

// Success icon styling
.text-success {
  color: #4caf50;
}

// Form styling
mat-form-field {
  width: 100%;
}

// Modal animation
.fixed {
  animation: fadeIn 0.3s ease;
}

@keyframes fadeIn {
  from {
    opacity: 0;
  }
  to {
    opacity: 1;
  }
}

// Responsive adjustments
@media (max-width: 768px) {
  .event-card {
    max-width: 400px;
    margin: 0 auto;
  }
}