<section class="newsletter-section py-16 bg-gray-100">
  <div class="container mx-auto px-4">
    <div class="max-w-4xl mx-auto bg-white rounded-lg shadow-md overflow-hidden">
      <div class="flex flex-col md:flex-row">
        <!-- Image Section -->
        <div class="md:w-2/5 bg-primary">
          <div class="h-full flex items-center justify-center p-8">
            <div class="text-white text-center">
              <mat-icon class="text-5xl mb-4">email</mat-icon>
              <h3 class="text-xl font-bold mb-2">Stay Updated</h3>
              <p class="text-sm opacity-90">Get the latest news and exclusive offers</p>
            </div>
          </div>
        </div>
        
        <!-- Form Section -->
        <div class="md:w-3/5 p-8">
          <h2 class="text-2xl font-semibold mb-2">Subscribe to our Newsletter</h2>
          <p class="text-gray-600 mb-6">Join our community of audiophiles and be the first to know about new products, special offers, and upcoming events.</p>
          
          <form [formGroup]="newsletterForm" (ngSubmit)="onSubmit()" class="space-y-4">
            <mat-form-field appearance="outline" class="w-full">
              <mat-label>Email Address</mat-label>
              <input matInput type="email" formControlName="email" placeholder="<EMAIL>">
              <mat-error *ngIf="newsletterForm.get('email')?.hasError('required')">
                Email is required
              </mat-error>
              <mat-error *ngIf="newsletterForm.get('email')?.hasError('email')">
                Please enter a valid email address
              </mat-error>
            </mat-form-field>
            
            <button mat-raised-button color="primary" type="submit" class="w-full py-2" [disabled]="newsletterForm.invalid || isSubmitting">
              <ng-container *ngIf="!isSubmitting">SUBSCRIBE</ng-container>
              <mat-spinner *ngIf="isSubmitting" diameter="24" class="mx-auto"></mat-spinner>
            </button>
            
            <p class="text-xs text-gray-500 text-center mt-4">
              By subscribing, you agree to our <a href="#" class="text-primary hover:underline">Privacy Policy</a>. 
              You can unsubscribe at any time.
            </p>
          </form>
        </div>
      </div>
    </div>
  </div>
</section>
