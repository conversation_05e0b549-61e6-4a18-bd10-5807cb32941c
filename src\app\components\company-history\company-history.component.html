<section class="company-history-section">
  <div class="history-container">
    <div class="section-header">
      <img src="https://cdn.shopify.com/s/files/1/0153/8863/files/Headphone-Zone-New-Team-photo.jpg?v=1737464840" alt="Headphone Zone Team" class="team-photo">
      <h2 class="section-title">This is Us. This is Headphone Zone.</h2>
    </div>

    <div class="timeline-container">
      <div class="timeline-navigation">
        <button class="prev-button" (click)="prevMilestone()">
          <span class="material-icons">arrow_back</span>
        </button>
        <div class="timeline-years">
          <span
            *ngFor="let milestone of milestones; let i = index"
            class="year-marker"
            [class.active]="i === currentMilestoneIndex"
            (click)="setCurrentMilestone(i)"
          >
            {{ milestone.year }}
          </span>
        </div>
        <button class="next-button" (click)="nextMilestone()">
          <span class="material-icons">arrow_forward</span>
        </button>
      </div>

      <div class="milestone-content">
        <div class="milestone-image">
          <img [src]="milestones[currentMilestoneIndex].image" [alt]="milestones[currentMilestoneIndex].title">
        </div>
        <div class="milestone-text">
          <h3 class="milestone-year">{{ milestones[currentMilestoneIndex].year }}</h3>
          <h4 class="milestone-title">{{ milestones[currentMilestoneIndex].title }}</h4>
          <p class="milestone-description">{{ milestones[currentMilestoneIndex].description }}</p>
        </div>
      </div>
    </div>
  </div>
</section>
