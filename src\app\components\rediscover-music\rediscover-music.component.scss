.rediscover-section {
  width: 100%;
  margin: 2rem 0;
  overflow: hidden;
}

.rediscover-container {
  max-width: 1200px;
  margin: 0 auto;
  position: relative;
}

.rediscover-slider {
  position: relative;
  height: 500px;
  overflow: hidden;
  border-radius: 8px;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
}

.rediscover-slide {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background-size: cover;
  background-position: center;
  opacity: 0;
  transition: opacity 0.5s ease;
  display: flex;
  align-items: center;
  padding: 2rem;

  &.active {
    opacity: 1;
    z-index: 1;
  }

  &::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg, rgba(0,0,0,0.7) 0%, rgba(0,0,0,0.4) 50%, rgba(0,0,0,0.1) 100%);
    z-index: -1;
  }
}

.slide-content {
  color: white;
  max-width: 600px;
  position: relative;
  z-index: 2;
  text-shadow: 0 1px 3px rgba(0, 0, 0, 0.3);
}

.artist-name {
  font-size: 1.2rem;
  text-transform: uppercase;
  letter-spacing: 2px;
  margin-bottom: 0.5rem;
  font-weight: 600;
}

.slide-title {
  font-size: 2.5rem;
  font-weight: 700;
  margin-bottom: 1rem;
  line-height: 1.2;
}

.slide-description {
  font-size: 1rem;
  line-height: 1.6;
  margin-bottom: 1.5rem;
  opacity: 0.9;
}

.slide-note {
  font-size: 1.1rem;
  margin-bottom: 2rem;
  font-style: italic;
}

.shop-button {
  display: inline-block;
  background-color: #ff5722;
  color: white;
  padding: 0.8rem 2rem;
  border-radius: 4px;
  font-weight: 600;
  text-transform: uppercase;
  letter-spacing: 1px;
  transition: all 0.3s ease;
  text-decoration: none;

  &:hover {
    background-color: darken(#ff5722, 10%);
    transform: translateY(-2px);
    box-shadow: 0 4px 8px rgba(0, 0, 0, 0.2);
  }
}

.slider-controls {
  display: flex;
  justify-content: center;
  align-items: center;
  margin-top: 1.5rem;
}

.prev-button,
.next-button {
  background: none;
  border: none;
  font-size: 1.5rem;
  color: #333;
  cursor: pointer;
  padding: 0.5rem;
  transition: all 0.3s ease;

  &:hover {
    color: #ff5722;
  }
}

.slider-dots {
  display: flex;
  gap: 0.5rem;
  margin: 0 1rem;
}

.dot {
  width: 12px;
  height: 12px;
  border-radius: 50%;
  background-color: #ccc;
  cursor: pointer;
  transition: all 0.3s ease;

  &.active {
    background-color: #ff5722;
    transform: scale(1.2);
  }

  &:hover {
    background-color: #aaa;
  }
}

@media (max-width: 768px) {
  .rediscover-slider {
    height: 400px;
  }

  .slide-content {
    max-width: 100%;
  }

  .slide-title {
    font-size: 2rem;
  }

  .slide-description {
    font-size: 0.9rem;
  }
}

@media (max-width: 576px) {
  .rediscover-slider {
    height: 500px;
  }

  .rediscover-slide {
    padding: 1.5rem;
    align-items: flex-start;
    justify-content: center;
    text-align: center;

    &::before {
      background: linear-gradient(0deg, rgba(0,0,0,0.8) 0%, rgba(0,0,0,0.5) 50%, rgba(0,0,0,0.3) 100%);
    }
  }

  .slide-content {
    margin-top: 2rem;
  }

  .slide-title {
    font-size: 1.8rem;
  }
}