<section class="product-grid py-12">
  <div class="container mx-auto px-4">
    <!-- Section Title -->
    <div class="text-center mb-8">
      <div *ngIf="isSearchActive; else defaultTitle">
        <h2 class="section-title inline-block">Search Results</h2>
      </div>
      <ng-template #defaultTitle>
        <h2 class="section-title inline-block">Best Sellers</h2>
        <p class="text-gray-600 mt-3 max-w-2xl mx-auto">Discover our most popular headphones and earphones, loved by
          audiophiles worldwide.</p>
      </ng-template>
    </div>

    <!-- Filters and Products Container -->
    <div class="flex flex-col lg:flex-row gap-6 mt-8">
      <!-- Filter Panel -->
      <aside class="w-full lg:w-64 flex-shrink-0">
        <div class="sticky top-24">
          <form [formGroup]="filterForm" class="bg-white border border-gray-200 rounded p-4">
            <div class="flex items-center justify-between mb-4">
              <h3 class="text-base font-semibold">Filters</h3>
              <button mat-button color="primary" class="text-xs" (click)="filterForm.reset({
                category: 'all',
                priceRange: [minPrice, maxPrice],
                rating: 0,
                availability: false,
                onSale: false
              }); resetPagination();">Reset All</button>
            </div>

            <!-- Category Filter -->
            <div class="mb-6">
              <h4 class="text-sm font-medium mb-3 pb-2 border-b border-gray-100">Category</h4>
              <div class="space-y-2">
                <mat-radio-group formControlName="category" class="flex flex-col space-y-2">
                  <mat-radio-button value="all" color="primary" class="text-sm">All Categories</mat-radio-button>
                  <mat-radio-button *ngFor="let category of categories" [value]="category" color="primary"
                    class="text-sm">
                    {{ category }}
                  </mat-radio-button>
                </mat-radio-group>
              </div>
            </div>

            <!-- Price Range Filter -->
            <div class="mb-6">
              <h4 class="text-sm font-medium mb-3 pb-2 border-b border-gray-100">Price Range</h4>
              <mat-slider [min]="minPrice" [max]="maxPrice" [step]="500" [discrete]="true" class="w-full">
                <input matSliderStartThumb formControlName="priceRange.min">
                <input matSliderEndThumb formControlName="priceRange.max">
              </mat-slider>
              <div class="flex justify-between text-xs text-gray-600 mt-2">
                <span>₹{{ filterForm.get('priceRange.min')?.value || minPrice }}</span>
                <span>₹{{ filterForm.get('priceRange.max')?.value || maxPrice }}</span>
              </div>
            </div>

            <!-- Rating Filter -->
            <div class="mb-6">
              <h4 class="text-sm font-medium mb-3 pb-2 border-b border-gray-100">Rating</h4>
              <div class="space-y-2">
                <div *ngFor="let rating of [4, 3, 2, 1]" class="flex items-center">
                  <mat-checkbox [checked]="(filterForm.get('rating')?.value || 0) >= rating"
                    (change)="filterForm.patchValue({rating: rating})"
                    color="primary" class="text-sm">
                    <div class="flex items-center">
                      <div class="flex">
                        <mat-icon *ngFor="let i of [1,2,3,4,5]" class="text-amber-400 text-sm">
                          {{ i <= rating ? 'star' : 'star_border' }} </mat-icon>
                      </div>
                      <span class="ml-1 text-xs">& Up</span>
                    </div>
                  </mat-checkbox>
                </div>
              </div>
            </div>

            <!-- Additional Filters -->
            <div class="space-y-3">
              <h4 class="text-sm font-medium mb-3 pb-2 border-b border-gray-100">Availability</h4>
              <mat-checkbox formControlName="availability" color="primary" class="text-sm">
                In Stock Only
              </mat-checkbox>
              <mat-checkbox formControlName="onSale" color="primary" class="text-sm">
                On Sale
              </mat-checkbox>
            </div>
          </form>
        </div>
      </aside>

      <!-- Products Section -->
      <div class="flex-1">
        <!-- Sort Controls -->
        <div class="flex flex-wrap items-center justify-between mb-6 bg-white border border-gray-200 rounded p-3">
          <div class="flex items-center">
            <span class="text-sm text-gray-600 mr-3">Sort by:</span>
            <mat-form-field appearance="outline" class="w-48 m-0">
              <mat-select [formControl]="sortControl" panelClass="sort-panel">
                <mat-option value="featured">Featured</mat-option>
                <mat-option value="newest">Newest</mat-option>
                <mat-option value="price-asc">Price: Low to High</mat-option>
                <mat-option value="price-desc">Price: High to Low</mat-option>
                <mat-option value="rating">Top Rated</mat-option>
              </mat-select>
            </mat-form-field>
          </div>

          <span class="text-sm text-gray-600">
            {{ displayedProducts.length }} products
          </span>
        </div>

        <!-- Products Grid -->
        <div class="grid grid-cols-1 sm:grid-cols-2 md:grid-cols-3 lg:grid-cols-4 gap-4 md:gap-6"
          [@staggerAnimation]="displayedProducts.length">
          <div *ngFor="let product of displayedProducts" class="product-card"
            (mouseenter)="showViewersNotification(product)">

            <!-- Discount Badge -->
            <div *ngIf="product.discountedPrice" class="absolute top-2 left-2 z-10">
              <div class="bg-primary text-white text-xs font-medium px-2 py-1 rounded product-badge">
                {{ getDiscountPercentage(product) }}% OFF
              </div>
            </div>

            <!-- New/Hot Badge -->
            <div *ngIf="product.isNew || product.isHot" class="absolute top-2 right-2 z-10">
              <div class="bg-secondary text-white text-xs font-medium px-2 py-1 rounded product-badge">
                {{ product.isNew ? 'NEW' : 'HOT' }}
              </div>
            </div>

            <!-- Product Badges -->
            <div *ngIf="product.badges && product.badges.length > 0" class="absolute bottom-2 left-2 z-10 flex flex-wrap">
              <app-product-badge
                *ngFor="let badge of product.badges"
                [badge]="badge"
                size="small">
              </app-product-badge>
            </div>

            <!-- Product Image -->
            <div class="product-image">
              <a [routerLink]="['/product', product.id]">
                <img [src]="product.imageUrl" [alt]="product.name" loading="lazy">
              </a>

              <!-- Quick Actions Overlay -->
              <div
                class="absolute inset-0 bg-black bg-opacity-0 group-hover:bg-opacity-10 transition-all duration-300 flex items-center justify-center">
                <div class="quick-actions opacity-0 group-hover:opacity-100 transition-opacity duration-300">
                  <button mat-mini-fab color="primary" aria-label="Quick view" class="mx-1"
                    [routerLink]="['/product', product.id]">
                    <mat-icon>visibility</mat-icon>
                  </button>
                  <button mat-mini-fab color="primary" aria-label="Add to wishlist" class="mx-1">
                    <mat-icon>favorite_border</mat-icon>
                  </button>
                </div>
              </div>
            </div>

            <!-- Product Info -->
            <div class="product-info">
              <h3 class="product-title">
                <a [routerLink]="['/product', product.id]">{{ product.name }}</a>
              </h3>

              <!-- Rating -->
              <div class="flex items-center mb-2">
                <div class="flex">
                  <mat-icon class="text-amber-400 text-sm" *ngFor="let i of [1,2,3,4,5]">
                    {{ i <= product.rating ? 'star' : (i - 0.5 <= product.rating ? 'star_half' : 'star_border') }} </mat-icon>
                </div>
                <span class="text-xs text-gray-500 ml-1">({{ product.reviewCount }})</span>
              </div>

              <!-- Price -->
              <div class="product-price">
                <span *ngIf="product.discountedPrice" class="original-price">
                  ₹{{ product.price.toFixed(2) }}
                </span>
                <span class="current-price">
                  ₹{{ (product.discountedPrice || product.price).toFixed(2) }}
                </span>
                <span *ngIf="product.discountedPrice" class="discount">
                  Save ₹{{ (product.price - product.discountedPrice).toFixed(2) }}
                </span>
              </div>

              <!-- Stock Status -->
              <div [ngSwitch]="getStockStatus(product.stock)" class="mt-2 text-xs">
                <span *ngSwitchCase="'out-of-stock'" class="text-error font-medium">
                  Out of Stock
                </span>
                <span *ngSwitchCase="'low-stock'" class="text-error font-medium">
                  Only {{ product.stock }} left!
                </span>
                <span *ngSwitchCase="'in-stock'" class="text-success font-medium">
                  In Stock
                </span>
              </div>

              <!-- Sound Signature -->
              <div *ngIf="product.soundSignature" class="mt-3">
                <app-sound-signature-indicator
                  [signature]="product.soundSignature"
                  size="small"
                  [showLabel]="false">
                </app-sound-signature-indicator>
              </div>

              <!-- Add to Cart Button -->
              <button (click)="addToCart(product, $event)"
                class="w-full mt-3 py-2 bg-orange-600 text-white rounded-none hover:bg-opacity-90 transition-all text-sm uppercase font-medium"
                [disabled]="product.stock === 0">
                {{ product.stock === 0 ? 'Out of Stock' : 'Add to Cart' }}
              </button>
            </div>
          </div>
        </div>

        <!-- Loading Indicator -->
        <div *ngIf="isLoading" class="flex justify-center my-8">
          <mat-spinner diameter="40"></mat-spinner>
        </div>

        <!-- View All Button -->
        <div class="text-center mt-10">
          <a routerLink="/collections/all"
            class="btn-outline inline-flex items-center px-6 py-2 border border-gray-800 text-gray-800 hover:bg-gray-800 hover:text-white transition-colors">
            <span>View All Products</span>
            <span class="ml-2">→</span>
          </a>
        </div>
      </div>
    </div>
  </div>
</section>