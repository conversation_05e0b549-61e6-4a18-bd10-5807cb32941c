// Headphonezone.in inspired styles
:root {
  --primary: #ff5500;
  --primary-rgb: 255, 85, 0;
  --secondary: #333333;
  --text-primary: #333333;
  --text-secondary: #666666;
  --text-light: #999999;
  --border-color: #e5e5e5;
  --background-light: #f9f9f9;
  --success: #4caf50;
  --error: #f44336;
  --error-rgb: 244, 67, 54;
}

// Breadcrumb styles
.breadcrumb {
  ul {
    list-style: none;
    padding: 0;
    margin: 0;
  }

  a {
    text-decoration: none;
    transition: color 0.2s ease;

    &:hover {
      color: var(--primary);
    }
  }
}

// Page title
h1.text-center {
  font-weight: 600;
  color: var(--text-primary);
  position: relative;
  display: inline-block;
  margin: 0 auto 2rem;
  padding-bottom: 0.75rem;

  &:after {
    content: '';
    position: absolute;
    bottom: 0;
    left: 50%;
    transform: translateX(-50%);
    width: 60px;
    height: 2px;
    background-color: var(--primary);
  }
}

.checkout-container {
  background-color: #ffffff;
  border-radius: 8px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.05);
  overflow: hidden;
}

.checkout-page {
  background-color: #ffffff;
  min-height: calc(100vh - 70px);
}

.cart-item {
  transition: all 0.3s ease;
  border-bottom: 1px solid #f0f0f0;
  padding: 1rem;

  &:hover {
    background-color: #f8f9fa;
  }

  &:last-child {
    border-bottom: none;
  }

  img {
    border-radius: 8px;
    object-fit: contain;
    background-color: #f5f5f5;
    padding: 5px;
    transition: transform 0.3s ease;

    &:hover {
      transform: scale(1.05);
    }
  }

  .item-details {
    h3 {
      font-weight: 600;
      margin-bottom: 0.25rem;
    }

    .text-gray-600 {
      font-size: 0.9rem;
      line-height: 1.4;
    }
  }

  .quantity-controls {
    button {
      background-color: #f0f0f0;
      border-radius: 4px;
      width: 28px;
      height: 28px;
      display: flex;
      align-items: center;
      justify-content: center;
      transition: all 0.2s ease;

      &:hover {
        background-color: #e0e0e0;
      }
    }

    span {
      font-weight: 600;
      min-width: 30px;
      text-align: center;
    }
  }
}

.order-summary {
  background-color: #ffffff;
  border-radius: 8px;
  border: 1px solid var(--border-color);
  padding: 1.5rem;

  h2 {
    font-weight: 600;
    margin-bottom: 1.5rem;
    color: var(--text-primary);
    font-size: 1.25rem;
    position: relative;
    padding-bottom: 0.75rem;

    &:after {
      content: '';
      position: absolute;
      bottom: 0;
      left: 0;
      width: 40px;
      height: 2px;
      background-color: var(--primary);
    }
  }

  .flex.justify-between {
    margin-bottom: 0.75rem;
    color: var(--text-secondary);
    font-size: 0.95rem;

    &:last-of-type {
      margin-bottom: 0;
    }
  }

  .border-t {
    border-color: var(--border-color);
  }

  .font-bold {
    font-weight: 600;
  }

  .total-row {
    font-size: 1.1rem;
    color: var(--text-primary);
    font-weight: 600;
  }
}

.text-success {
  color: var(--success);
}

// Payment methods
.payment-methods {
  .payment-option {
    border: 2px solid #f0f0f0;
    border-radius: 8px;
    padding: 1rem;
    transition: all 0.2s ease;
    cursor: pointer;

    &:hover, &.selected {
      border-color: var(--primary);
      background-color: rgba(var(--primary-rgb), 0.05);
    }

    img {
      height: 24px;
      object-fit: contain;
    }
  }
}

// Payment method cards - Headphonezone.in style
.payment-method-card {
  cursor: pointer;
  transition: all 0.3s ease;
  border: 1px solid var(--border-color);
  border-radius: 4px;
  overflow: hidden;

  &:hover {
    border-color: var(--primary);
  }

  &.selected {
    border-color: var(--primary);
    background-color: rgba(var(--primary-rgb), 0.05);

    mat-icon {
      color: var(--primary);
    }

    &:after {
      content: '';
      position: absolute;
      top: 10px;
      right: 10px;
      width: 16px;
      height: 16px;
      border-radius: 50%;
      background-color: var(--primary);
      background-image: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 24 24' fill='white'%3E%3Cpath d='M9 16.17L4.83 12l-1.42 1.41L9 19 21 7l-1.41-1.41L9 16.17z'/%3E%3C/svg%3E");
      background-size: 12px;
      background-position: center;
      background-repeat: no-repeat;
    }
  }

  mat-card-content {
    padding: 1.25rem 1rem;
  }

  mat-icon {
    transition: color 0.3s ease;
    font-size: 1.75rem;
    height: 1.75rem;
    width: 1.75rem;
    color: var(--text-secondary);
  }

  h3 {
    font-size: 0.95rem;
    margin: 0.5rem 0;
    color: var(--text-primary);
  }

  p {
    font-size: 0.8rem;
    color: var(--text-light);
    margin: 0;
  }
}

// Animation for the spinner
.animate-spin {
  animation: spin 1s linear infinite;
}

@keyframes spin {
  from {
    transform: rotate(0deg);
  }
  to {
    transform: rotate(360deg);
  }
}

// Cart item animation
.animate-item-enter {
  animation: itemEnter 0.3s ease forwards;
}

@keyframes itemEnter {
  from {
    opacity: 0;
    transform: translateX(-20px);
  }
  to {
    opacity: 1;
    transform: translateX(0);
  }
}

// Checkout button - Headphonezone.in style
.btn-primary {
  width: 100%;
  padding: 0.75rem;
  font-weight: 600;
  font-size: 1rem;
  border-radius: 4px;
  transition: all 0.3s ease;
  background-color: var(--primary) !important;
  color: white !important;
  text-transform: uppercase;
  letter-spacing: 0.5px;

  &:hover {
    background-color: darken(#ff5500, 5%) !important;
    box-shadow: 0 4px 8px rgba(var(--primary-rgb), 0.3);
  }

  &:disabled {
    opacity: 0.7;
    cursor: not-allowed;
    background-color: #cccccc !important;
    color: #666666 !important;
  }
}

// Section headers
.section-header {
  font-size: 1.25rem;
  font-weight: 600;
  color: var(--text-primary);
  margin-bottom: 1.25rem;
  position: relative;
  padding-bottom: 0.75rem;

  &:after {
    content: '';
    position: absolute;
    bottom: 0;
    left: 0;
    width: 40px;
    height: 2px;
    background-color: var(--primary);
  }
}

// Form sections
.form-section {
  background-color: #ffffff;
  border: 1px solid var(--border-color);
  border-radius: 4px;
  padding: 1.5rem;
  margin-bottom: 1.5rem;
}

// Form field customization - Headphonezone.in style
::ng-deep {
  .mat-mdc-form-field {
    width: 100%;
    margin-bottom: 0.5rem;

    .mat-mdc-form-field-flex {
      background-color: #ffffff;
    }

    .mat-mdc-form-field-infix {
      padding: 0.5em 0;
    }

    .mat-mdc-text-field-wrapper {
      border-radius: 4px;
      border: 1px solid var(--border-color);
      padding-left: 0.5rem;
      padding-right: 0.5rem;
      background-color: #ffffff;

      &:hover {
        border-color: #999999;
      }
    }

    .mat-mdc-form-field-label {
      color: var(--text-secondary);
      font-size: 0.9rem;
    }

    &.mat-focused {
      .mat-mdc-text-field-wrapper {
        border-color: var(--primary);
      }
    }

    &.mat-mdc-form-field-invalid {
      .mat-mdc-text-field-wrapper {
        border-color: var(--error);
        background-color: rgba(var(--error-rgb), 0.05);
      }
    }
  }

  // Remove underline
  .mdc-line-ripple {
    display: none;
  }

  // Customize select dropdown
  .mat-mdc-select-panel {
    border-radius: 4px !important;
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1) !important;
  }
}

// Quantity input
.mat-mdc-form-field.w-20 {
  .mat-mdc-form-field-flex {
    padding: 0 8px;
  }

  input {
    text-align: center;
  }
}

// Sticky summary
.sticky {
  @media (min-width: 1024px) {
    position: sticky;
    top: 24px;
  }
}

// Button states
button[mat-raised-button] {
  &:disabled {
    opacity: 0.7;
  }

  mat-spinner {
    display: inline-block;
    vertical-align: middle;
    margin-right: 8px;
  }
}

// Section transitions
.bg-white {
  transition: transform 0.3s ease, box-shadow 0.3s ease;

  &:hover {
    box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
  }
}

// Material checkbox customization - Headphonezone.in style
::ng-deep {
  .mat-mdc-checkbox {
    .mdc-checkbox {
      .mdc-checkbox__background {
        border-radius: 2px;
        border-color: #999999;
      }

      .mdc-checkbox__native-control:enabled:checked ~ .mdc-checkbox__background {
        background-color: var(--primary);
        border-color: var(--primary);
      }
    }
  }

  // Trust badges section
  .mt-6.border-t {
    border-color: var(--border-color);

    img {
      transition: opacity 0.2s ease;

      &:hover {
        opacity: 0.8;
      }
    }
  }

  // Links
  a.text-primary {
    color: var(--primary);
    transition: all 0.2s ease;

    &:hover {
      text-decoration: underline;
    }
  }
}

// Error messages
mat-error {
  font-size: 0.75rem;
  margin-top: 4px;
}

// Form section spacing
.space-y-8 > :not([hidden]) ~ :not([hidden]) {
  margin-top: 2rem;
}

// Responsive adjustments
@media (max-width: 1023px) {
  .lg\:w-96 {
    width: 100%;
  }
}

@media (max-width: 768px) {
  .checkout-container {
    flex-direction: column;
  }

  .order-summary {
    margin-top: 1.5rem;
  }
}

@media (max-width: 640px) {
  .cart-item {
    padding: 1rem 0.5rem;

    .flex-row {
      flex-direction: column;
    }

    img {
      width: 80px;
      height: 80px;
      margin-bottom: 0.5rem;
    }
  }
}

// Loading state overlay
.processing-overlay {
  @apply fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50;
}

// Reduced motion
@media (prefers-reduced-motion: reduce) {
  .payment-method-card,
  .cart-item,
  .bg-white,
  .animate-item-enter {
    transition: none !important;
    animation: none !important;
  }
}