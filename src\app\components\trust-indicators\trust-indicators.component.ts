import { Component, OnInit } from '@angular/core';
import { CommonModule } from '@angular/common';
import { MaterialModule } from '../../material/material.module';
import {
  trigger,
  transition,
  style,
  animate,
  query,
  stagger,
} from '@angular/animations';

interface TrustPoint {
  icon: string;
  title: string;
  description: string;
  metric?: string;
  image?: string;
}

@Component({
  selector: 'app-trust-indicators',
  standalone: true,
  imports: [CommonModule, MaterialModule],
  templateUrl: './trust-indicators.component.html',
  styleUrl: './trust-indicators.component.scss',
  animations: [
    trigger('staggerAnimation', [
      transition('* => *', [
        query(
          ':enter',
          [
            style({ opacity: 0, transform: 'translateY(20px)' }),
            stagger(100, [
              animate(
                '0.5s ease',
                style({ opacity: 1, transform: 'translateY(0)' })
              ),
            ]),
          ],
          { optional: true }
        ),
      ]),
    ]),
  ],
})
export class TrustIndicatorsComponent implements OnInit {
  trustPoints: TrustPoint[] = [
    {
      icon: 'local_shipping',
      title: 'Free & Fast Shipping',
      description: 'Free shipping on all orders above ₹1,500',
      metric: '1-3 business days',
      image: '//www.headphonezone.in/cdn/shop/files/Headphone-Zone-Free-Shipping-Icon.png?v=1686393671'
    },
    {
      icon: 'replay',
      title: 'Easy Returns',
      description: 'Hassle-free 7-day return policy',
      metric: '7-day returns',
      image: '//www.headphonezone.in/cdn/shop/files/Headphone-Zone-Easy-Returns-Icon.png?v=1686393671'
    },
    {
      icon: 'verified_user',
      title: 'Authentic Products',
      description: '100% genuine products, authorized dealer',
      metric: 'Brand warranty',
      image: '//www.headphonezone.in/cdn/shop/files/Headphone-Zone-Authentic-Products-Icon.png?v=1686393671'
    },
    {
      icon: 'support_agent',
      title: 'Expert Support',
      description: 'Audiophile experts to help you choose',
      metric: 'Mon-Sat, 11AM-7PM',
      image: '//www.headphonezone.in/cdn/shop/files/Headphone-Zone-Expert-Support-Icon.png?v=1686393671'
    },
    {
      icon: 'payments',
      title: 'Secure Payments',
      description: 'Multiple payment options available',
      metric: 'EMI available',
      image: '//www.headphonezone.in/cdn/shop/files/Headphone-Zone-Secure-Payments-Icon.png?v=1686393671'
    },
  ];

  visiblePoints: TrustPoint[] = [];

  ngOnInit(): void {
    this.animatePoints();
  }

  private animatePoints(): void {
    // Stagger the appearance of trust points
    this.trustPoints.forEach((point, index) => {
      setTimeout(() => {
        this.visiblePoints = [...this.trustPoints.slice(0, index + 1)];
      }, index * 200);
    });
  }

  getIconColor(index: number): string {
    const colors = ['primary', 'accent', 'warn'];
    return colors[index % colors.length];
  }
}
