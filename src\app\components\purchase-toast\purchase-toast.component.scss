.purchase-toast {
  max-width: 350px;

  .text-success {
    color: var(--success);
  }

  .text-warning {
    color: var(--warning);
  }

  .border-success {
    border-color: var(--success);
  }

  .border-warning {
    border-color: var(--warning);
  }
}

// Add a subtle shadow effect
.bg-white {
  box-shadow: 0 10px 15px -3px rgba(0, 0, 0, 0.1), 0 4px 6px -2px rgba(0, 0, 0, 0.05);
}

.toast-container {
  width: 100%;
  max-width: 400px;
  pointer-events: none; // Allow clicking through the container
  will-change: transform, opacity;

  &.toast-active {
    pointer-events: auto;
  }

  // Make buttons clickable
  button {
    pointer-events: all;
  }
}

.toast-content {
  width: 100%;
  pointer-events: all;
  border-left: 4px solid transparent;
  
  &.viewer-notification {
    border-left-color: var(--warning);

    .notification-icon {
      background-color: var(--warning);
    }
  }
}

.notification-icon {
  min-width: 56px;

  &.success {
    background-color: var(--success);
  }

  &.warning {
    background-color: var(--warning);
  }

  mat-icon {
    filter: drop-shadow(0 2px 4px rgba(0, 0, 0, 0.2));
  }
}

// Progress bar animation
.animate-progress {
  animation: progress 5s linear forwards;
  background: linear-gradient(
    90deg,
    var(--primary-light) 0%,
    var(--primary) 100%
  );
}

.progress-bar {
  will-change: width;
  transform-origin: left;
  background: linear-gradient(90deg, var(--primary) 0%, var(--primary-light) 100%);
}

@keyframes progress {
  from {
    width: 100%;
  }
  to {
    width: 0%;
  }
}

// Sound toggle button
.sound-toggle {
  opacity: 0.7;
  transition: all 0.2s ease;

  &:hover {
    opacity: 1;
    transform: scale(1.1);
  }

  mat-icon {
    font-size: 20px;
    width: 20px;
    height: 20px;
    line-height: 20px;
  }
}

// Elevation animation on hover
.toast-content:hover {
  box-shadow: 0 8px 16px rgba(0, 0, 0, 0.1);
}

// Timestamp style
.text-xs.text-gray-500 {
  letter-spacing: 0.025em;
}

// Responsive adjustments
@media (max-width: 640px) {
  .toast-container {
    max-width: calc(100% - 32px);
    margin: 0 16px;
  }

  :host {
    .toast-container {
      left: 1rem;
      right: 1rem;
      bottom: 1rem;
      max-width: none;
    }
  }
}

// Reduced motion preferences
@media (prefers-reduced-motion: reduce) {
  .animate-progress {
    animation: none;
    width: 100%;
  }

  .toast-content,
  .sound-toggle {
    transition: none;
  }

  .toast-container,
  .icon-container,
  .progress-bar {
    transition: none !important;
    animation: none !important;
  }
}

// High contrast mode support
@media (forced-colors: active) {
  .progress-bar {
    background: CanvasText;
  }

  .icon-container {
    border: 1px solid CanvasText;
  }
}

// Print styles
@media print {
  :host {
    display: none;
  }
}

:host {
  display: block;
}

// Icon animations
.icon-container {
  will-change: transform;
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);

  &:hover {
    transform: scale(1.1);
  }
}