import { Component, OnInit } from '@angular/core';
import { CommonModule } from '@angular/common';
import { RouterModule } from '@angular/router';
import { MaterialModule } from '../../material/material.module';
import { HeaderComponent } from '../../components/header/header.component';
import { FooterComponent } from '../../components/footer/footer.component';
import { AuthService, User } from '../../services/auth.service';

@Component({
  selector: 'app-account',
  standalone: true,
  imports: [
    CommonModule,
    RouterModule,
    MaterialModule,
    HeaderComponent,
    FooterComponent
  ],
  templateUrl: './account.component.html',
  styleUrls: ['./account.component.scss']
})
export class AccountComponent implements OnInit {
  user: User | null = null;
  activeTab = 'orders';
  
  navItems = [
    { id: 'orders', label: 'Orders', icon: 'shopping_bag' },
    { id: 'wishlist', label: 'Wishlist', icon: 'favorite_border' },
    { id: 'addresses', label: 'Addresses', icon: 'location_on' },
    { id: 'profile', label: 'Profile', icon: 'person' },
    { id: 'settings', label: 'Settings', icon: 'settings' }
  ];

  constructor(private authService: AuthService) {}

  ngOnInit(): void {
    this.authService.currentUser$.subscribe(user => {
      this.user = user;
    });
  }

  setActiveTab(tabId: string): void {
    this.activeTab = tabId;
  }

  logout(): void {
    this.authService.logout();
    // Navigate to home page
  }
}
