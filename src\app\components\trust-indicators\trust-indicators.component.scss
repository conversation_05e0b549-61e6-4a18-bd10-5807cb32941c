.trust-card {
  border-radius: 8px;
  border-bottom: 3px solid transparent;
  transition: all 0.3s ease;

  &:hover {
    border-bottom-color: var(--primary);
    transform: translateY(-5px);

    .icon-container {
      background-color: var(--primary);

      mat-icon {
        color: white;
      }
    }
  }
}

.icon-container {
  transition: all 0.3s ease;
}

.trust-indicators {
  background: linear-gradient(to bottom, #f8f9fa 0%, #ffffff 100%);
  position: relative;
  overflow: hidden;

  &::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    height: 1px;
    background: linear-gradient(to right, transparent, rgba(0,0,0,0.1), transparent);
  }
}

.section-title {
  position: relative;
  display: inline-block;
  padding-bottom: 0.5rem;
  margin: 0 auto;

  &::after {
    content: '';
    position: absolute;
    bottom: 0;
    left: 50%;
    transform: translateX(-50%);
    width: 60px;
    height: 3px;
    background: var(--primary);
    border-radius: 2px;
  }
}

.trust-point {
  transform: translateY(0);
  backdrop-filter: blur(10px);
  border: 1px solid rgba(255, 255, 255, 0.1);
  
  &:hover {
    transform: translateY(-5px);

    .icon-circle {
      transform: scale(1.1);
    }

    .metric-badge {
      transform: translateY(-2px);
    }
  }
}

.icon-circle {
  width: 64px;
  height: 64px;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  background: rgba(var(--primary-rgb), 0.1);
  transition: transform 0.3s ease;

  mat-icon {
    font-size: 32px;
    height: 32px;
    width: 32px;
    
    &[color="primary"] {
      color: var(--primary);
    }
    
    &[color="accent"] {
      color: var(--accent);
    }
    
    &[color="warn"] {
      color: var(--warn);
    }
  }
}

.metric-badge {
  display: inline-block;
  padding: 0.5rem 1rem;
  border-radius: 20px;
  background: linear-gradient(135deg, var(--primary) 0%, var(--primary-dark) 100%);
  color: white;
  font-size: 0.875rem;
  font-weight: 600;
  transition: all 0.3s ease;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

// Staggered animation delay
@for $i from 1 through 5 {
  .trust-point:nth-child(#{$i}) {
    transition-delay: #{$i * 0.1}s;
  }
}

// Responsive adjustments
@media (max-width: 768px) {
  .trust-card {
    margin-bottom: 1rem;
  }

  .trust-point {
    &:hover {
      transform: none;
    }
  }
}

// Reduced motion
@media (prefers-reduced-motion: reduce) {
  .trust-point,
  .icon-circle,
  .metric-badge {
    transition: none !important;
    animation: none !important;
  }
}