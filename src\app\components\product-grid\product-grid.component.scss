.product-card {
  position: relative;
  border: 1px solid #e5e7eb;
  border-radius: 0;
  overflow: hidden;
  transition: all 0.3s ease;
  background-color: #fff;
  height: 100%;
  display: flex;
  flex-direction: column;

  &:hover {
    box-shadow: 0 5px 15px rgba(0, 0, 0, 0.08);
    transform: translateY(-3px);
  }

  // Product Badge
  .product-badge {
    font-size: 0.7rem;
    font-weight: 600;
    letter-spacing: 0.5px;
    border-radius: 4px;
  }

  // Product Image
  .product-image {
    position: relative;
    padding: 1rem;
    background-color: #f9fafb;
    aspect-ratio: 1/1;
    display: flex;
    align-items: center;
    justify-content: center;

    img {
      max-height: 100%;
      object-fit: contain;
      transition: transform 0.5s ease;
    }

    &:hover img {
      transform: scale(1.05);
    }
  }

  // Quick Actions
  .quick-actions {
    button {
      width: 36px;
      height: 36px;

      ::ng-deep .mat-mdc-button-touch-target {
        width: 36px;
        height: 36px;
      }

      mat-icon {
        font-size: 18px;
        height: 18px;
        width: 18px;
        line-height: 18px;
      }
    }
  }

  // Product Info
  .product-info {
    padding: 1rem;
    flex-grow: 1;
    display: flex;
    flex-direction: column;

    .product-title {
      font-weight: 500;
      font-size: 1rem;
      margin-bottom: 0.5rem;
      line-height: 1.4;
      display: -webkit-box;
      line-clamp: 2;
      -webkit-line-clamp: 2;
      -webkit-box-orient: vertical;
      overflow: hidden;
      color: #1f2937;

      a {
        color: inherit;
        text-decoration: none;

        &:hover {
          color: var(--primary);
        }
      }
    }

    // Price
    .product-price {
      display: flex;
      flex-wrap: wrap;
      align-items: baseline;
      gap: 0.5rem;
      margin: 0.5rem 0;

      .original-price {
        color: #9ca3af;
        text-decoration: line-through;
        font-size: 0.875rem;
      }

      .current-price {
        color: var(--primary);
        font-weight: 600;
        font-size: 1.125rem;
      }

      .discount {
        color: #10b981;
        font-size: 0.75rem;
        font-weight: 500;
      }
    }

    // Stock Status
    .text-error {
      color: #ef4444;
    }

    .text-warning {
      color: #f59e0b;
    }

    .text-success {
      color: #10b981;
    }

    // Add to Cart Button
    button {
      background-color: #ff5500;
      border: none;
      font-weight: 600;
      letter-spacing: 0.5px;
      transition: all 0.3s ease;
      margin-top: auto;
      border-radius: 0;

      &:hover:not([disabled]) {
        background-color: darken(#ff5500, 5%);
        transform: translateY(-2px);
      }

      &[disabled] {
        background-color: #d1d5db;
        color: #6b7280;
        cursor: not-allowed;
      }
    }
  }
}

// Product grid
.product-grid {
  background-color: #f9fafb;

  // Section title styling
  .section-title {
    position: relative;
    font-weight: 700;
    color: #333;
    font-size: 1.875rem;

    &::after {
      content: '';
      position: absolute;
      bottom: -10px;
      left: 50%;
      transform: translateX(-50%);
      width: 50px;
      height: 3px;
      background-color: var(--primary);
      border-radius: 3px;
    }
  }

  // Sort panel
  .sort-panel {
    max-width: 300px;
  }
}

// Filter panel styling
form[formGroup="filterForm"] {
  position: sticky;
  top: 24px;

  h4 {
    font-weight: 600;
    color: #374151;
  }
}

// Material form field customization
::ng-deep {
  .mat-mdc-form-field-subscript-wrapper {
    display: none;
  }

  .mat-mdc-form-field {
    .mat-mdc-text-field-wrapper {
      padding-left: 0.5rem;
      padding-right: 0.5rem;
      background-color: #ffffff;
    }
  }

  .mat-mdc-slider {
    margin: 0;

    .mdc-slider__thumb-knob {
      background-color: var(--primary);
      border-color: var(--primary);
    }

    .mdc-slider__track--active {
      background-color: var(--primary);
    }
  }

  .mat-mdc-radio-button {
    .mdc-radio__background {
      .mdc-radio__outer-circle,
      .mdc-radio__inner-circle {
        border-color: var(--primary);
      }
    }

    &.mat-mdc-radio-checked {
      .mdc-radio__background {
        .mdc-radio__inner-circle {
          background-color: var(--primary);
        }
      }
    }
  }

  .mat-mdc-checkbox {
    .mdc-checkbox__background {
      border-color: #9ca3af;
    }

    &.mat-mdc-checkbox-checked {
      .mdc-checkbox__background {
        background-color: var(--primary);
        border-color: var(--primary);
      }
    }
  }
}

// View All Button
.btn-outline {
  position: relative;
  overflow: hidden;
  z-index: 1;
  transition: all 0.3s ease;
  border-radius: 0;

  &::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    width: 0;
    height: 100%;
    background-color: #333;
    z-index: -1;
    transition: width 0.3s ease;
  }

  &:hover {
    color: white;

    &::before {
      width: 100%;
    }

    span:last-child {
      transform: translateX(4px);
    }
  }

  span:last-child {
    transition: transform 0.3s ease;
  }
}

// Staggered animation for cards
@for $i from 1 through 12 {
  .product-card:nth-child(#{$i}) {
    animation-delay: #{$i * 0.1}s;
  }
}

// Loading spinner
.mat-mdc-progress-spinner {
  ::ng-deep circle {
    stroke: var(--primary);
  }
}

// Responsive adjustments
@media (max-width: 768px) {
  form[formGroup="filterForm"] {
    position: static;
  }

  .product-card {
    .product-image {
      aspect-ratio: 4/3;
    }

    &:hover {
      transform: translateY(-3px);
    }
  }
}

// Reduced motion preferences
@media (prefers-reduced-motion: reduce) {
  .product-card,
  .product-image img,
  .btn-outline::before,
  .btn-outline mat-icon {
    transition: none !important;
    animation: none !important;
  }
}