import { Component, Input } from '@angular/core';
import { CommonModule } from '@angular/common';
import { ProductBadge } from '../../models/product.model';

@Component({
  selector: 'app-product-badge',
  standalone: true,
  imports: [CommonModule],
  templateUrl: './product-badge.component.html',
  styleUrl: './product-badge.component.scss'
})
export class ProductBadgeComponent {
  @Input() badge!: ProductBadge;
  @Input() size: 'small' | 'medium' | 'large' = 'medium';

  getBadgeIcon(badge: ProductBadge): string {
    const icons: Record<ProductBadge, string> = {
      'value-for-money': 'savings',
      'ideal-for-beginners': 'school',
      'staff-pick': 'verified',
      'award-winner': 'emoji_events',
      'best-seller': 'trending_up',
      'limited-edition': 'star'
    };

    return icons[badge] || 'label';
  }

  getBadgeLabel(badge: ProductBadge): string {
    const labels: Record<ProductBadge, string> = {
      'value-for-money': 'Value for Money',
      'ideal-for-beginners': 'Ideal for Beginners',
      'staff-pick': 'Staff Pick',
      'award-winner': 'Award Winner',
      'best-seller': 'Best Seller',
      'limited-edition': 'Limited Edition'
    };

    return labels[badge] || badge;
  }

  getBadgeColor(badge: ProductBadge): string {
    const colors: Record<ProductBadge, string> = {
      'value-for-money': 'green',
      'ideal-for-beginners': 'blue',
      'staff-pick': 'purple',
      'award-winner': 'gold',
      'best-seller': 'red',
      'limited-edition': 'black'
    };

    return colors[badge] || 'gray';
  }
}
