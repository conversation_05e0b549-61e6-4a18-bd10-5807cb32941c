import { Component, OnInit } from '@angular/core';
import { CommonModule } from '@angular/common';
import { RouterModule } from '@angular/router';
import { trigger, transition, style, animate } from '@angular/animations';

interface MusicSlide {
  artist: string;
  title: string;
  description: string;
  backgroundImage: string;
}

@Component({
  selector: 'app-rediscover-music',
  standalone: true,
  imports: [CommonModule, RouterModule],
  templateUrl: './rediscover-music.component.html',
  styleUrl: './rediscover-music.component.scss',
  animations: [
    trigger('fadeAnimation', [
      transition(':enter', [
        style({ opacity: 0 }),
        animate('500ms', style({ opacity: 1 })),
      ]),
      transition(':leave', [
        animate('500ms', style({ opacity: 0 }))
      ])
    ])
  ]
})
export class RediscoverMusicComponent implements OnInit {
  slides: MusicSlide[] = [
    {
      artist: 'PINK FLOYD',
      title: 'Heard The Dark Side of the Moon a gazillion times?',
      description: 'But have you heard the amazing the 3-dimensional imaging of the cash registers and jingling coins on Money? Or the subtle double tracked harmonies sung by <PERSON> and <PERSON> on Time? This is music that deserves to be experienced, not just heard. Good sounding headphones can bring music to life.',
      backgroundImage: '//www.headphonezone.in/cdn/shop/files/Headphone-Zone-Pink-Floyd-Banner.jpg?v=1651151051'
    },
    {
      artist: 'A.R. RAHMAN',
      title: 'Heard Dil Se a gazillion times?',
      description: 'But have you heard the sound of Guy Pratt\'s bass guitar interplaying with Sivamani\'s percussions on Dil Se Re? Or the sounds of crisp attacking Mridangams and Thavils setting the stage for Lata Mangeshkar\'s singing on Jiya Jale? This is music that deserves to be experienced, not just heard. Good sounding headphones can bring music to life.',
      backgroundImage: '//www.headphonezone.in/cdn/shop/files/Headphone-Zone-AR-Rahman-Banner.jpg?v=1651151051'
    },
    {
      artist: 'THE EAGLES',
      title: 'Heard Hotel California a gazillion times?',
      description: 'But have you heard the sound of 2 nylon string spanish guitars battling each with a third 12 string acoustic guitar laying the foundation to an epic double-solo at the end of Hotel California? Or have you heard Don Henley\'s vocals accompanied by a 5 part harmony coming together over the iconic sound of a fretless bass guitar on New York Minute? Good sounding headphones can bring music to life.',
      backgroundImage: '//www.headphonezone.in/cdn/shop/files/Headphone-Zone-Eagles-Banner.jpg?v=1651151051'
    },
    {
      artist: 'MICHAEL JACKSON',
      title: 'Heard Thriller a gazillion times?',
      description: 'But have you heard the sound of crisp and thick Kick Drum along with that iconic bass line setting the groove to Billie Jean? This is music that deserves to be experienced, not just heard. Good sounding headphones can bring music to life.',
      backgroundImage: '//www.headphonezone.in/cdn/shop/files/Headphone-Zone-Michael-Jackson-Banner.jpg?v=1651151051'
    }
  ];

  currentSlideIndex = 0;

  ngOnInit(): void {
    // Auto-rotate slides every 5 seconds
    setInterval(() => {
      this.nextSlide();
    }, 5000);
  }

  nextSlide(): void {
    this.currentSlideIndex = (this.currentSlideIndex + 1) % this.slides.length;
  }

  prevSlide(): void {
    this.currentSlideIndex = (this.currentSlideIndex - 1 + this.slides.length) % this.slides.length;
  }

  setCurrentSlide(index: number): void {
    this.currentSlideIndex = index;
  }
}
