import {
  Component,
  OnInit,
  ElementRef,
  ViewChild,
  NgZone,
  OnDestroy,
  Input,
} from '@angular/core';
import { CommonModule } from '@angular/common';
import { MaterialModule } from '../../material/material.module';
import { SoundSignature } from '../../models/product.model';

@Component({
  selector: 'app-sound-signature-indicator',
  standalone: true,
  imports: [CommonModule, MaterialModule],
  templateUrl: './sound-signature-indicator.component.html',
  styleUrl: './sound-signature-indicator.component.scss',
})
export class SoundSignatureIndicatorComponent implements OnInit, OnDestroy {
  @ViewChild('counter') counterElement!: ElementRef;
  @Input() signature?: SoundSignature;
  @Input() showLabel: boolean = true;

  musicLoversCount = 0;
  targetCount = 150000;
  isCounterVisible = false;
  private observer: IntersectionObserver | null = null;
  private countStartDate = new Date('2025-05-03');

  constructor(private ngZone: NgZone) {}

  ngOnInit(): void {
    this.calculateTargetCount();
    this.setupIntersectionObserver();
  }

  ngOnDestroy(): void {
    if (this.observer) {
      this.observer.disconnect();
    }
  }

  private calculateTargetCount(): void {
    const today = new Date();
    const diffTime = Math.abs(today.getTime() - this.countStartDate.getTime());
    const diffDays = Math.ceil(diffTime / (1000 * 60 * 60 * 24));
    this.targetCount = 150000 + diffDays * 1000;
  }

  private setupIntersectionObserver(): void {
    this.observer = new IntersectionObserver(
      (entries) => {
        entries.forEach((entry) => {
          if (entry.isIntersecting && !this.isCounterVisible) {
            this.isCounterVisible = true;
            this.animateCount();
          }
        });
      },
      { threshold: 0.5 }
    );

    setTimeout(() => {
      if (this.counterElement) {
        this.observer?.observe(this.counterElement.nativeElement);
      }
    });
  }

  private animateCount(): void {
    const duration = 2000;
    const steps = 60;
    const increment = this.targetCount / steps;
    const stepDuration = duration / steps;
    let currentStep = 0;

    this.ngZone.runOutsideAngular(() => {
      const counter = setInterval(() => {
        currentStep++;
        this.ngZone.run(() => {
          this.musicLoversCount = Math.min(
            Math.round(increment * currentStep),
            this.targetCount
          );
        });

        if (currentStep === steps) {
          clearInterval(counter);
        }
      }, stepDuration);
    });
  }
}
