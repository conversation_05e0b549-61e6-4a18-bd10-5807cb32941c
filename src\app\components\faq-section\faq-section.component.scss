.faq-section {
  padding: 4rem 0;
  background-color: #fff;
}

.faq-container {
  max-width: 1200px;
  margin: 0 auto;
  padding: 0 1.5rem;
}

.section-header {
  text-align: center;
  margin-bottom: 3rem;
}

.section-title {
  font-size: 2.5rem;
  font-weight: 700;
  color: #333;
  margin-bottom: 1rem;
}

.section-subtitle {
  font-size: 1.1rem;
  color: #666;
  max-width: 700px;
  margin: 0 auto;
}

.faq-content {
  display: flex;
  flex-wrap: wrap;
  gap: 2rem;
}

.faq-list {
  flex: 1;
  min-width: 300px;
}

.faq-item {
  margin-bottom: 1rem;
  border: 1px solid #eee;
  border-radius: 8px;
  overflow: hidden;
  transition: all 0.3s ease;

  &.open {
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.05);

    .faq-question {
      background-color: #f9f9f9;
    }
  }
}

.faq-question {
  padding: 1.2rem;
  display: flex;
  justify-content: space-between;
  align-items: center;
  cursor: pointer;
  transition: background-color 0.3s ease;

  &:hover {
    background-color: #f9f9f9;
  }

  h3 {
    font-size: 1.1rem;
    font-weight: 600;
    color: #333;
    margin: 0;
  }

  .toggle-icon {
    color: #666;
    transition: transform 0.3s ease;

    .open & {
      transform: rotate(180deg);
    }
  }
}

.faq-answer {
  max-height: 0;
  overflow: hidden;
  transition: max-height 0.3s ease, padding 0.3s ease;

  &.visible {
    max-height: 500px;
    padding: 0 1.2rem 1.2rem;
  }

  p {
    margin: 0;
    color: #666;
    line-height: 1.6;
  }
}

.support-info {
  flex: 1;
  min-width: 300px;
  background-color: #f9f9f9;
  padding: 2rem;
  border-radius: 8px;
  text-align: center;
}

.support-image {
  max-width: 100%;
  height: auto;
  margin-bottom: 1.5rem;
}

.support-text {
  font-size: 1rem;
  color: #666;
  margin-bottom: 2rem;
}

.contact-options {
  display: flex;
  flex-direction: column;
  gap: 1rem;
}

.contact-button {
  display: block;
  padding: 1rem;
  background-color: #fff;
  color: #333;
  text-decoration: none;
  border-radius: 4px;
  font-weight: 600;
  transition: all 0.3s ease;
  border: 1px solid #ddd;

  &:hover {
    background-color: #ff5722;
    color: white;
    border-color: #ff5722;
    transform: translateY(-2px);
    box-shadow: 0 4px 8px rgba(0, 0, 0, 0.1);
  }
}

@media (max-width: 768px) {
  .section-title {
    font-size: 2rem;
  }

  .faq-content {
    flex-direction: column;
  }

  .support-info {
    order: -1;
    margin-bottom: 2rem;
  }
}

@media (max-width: 576px) {
  .section-title {
    font-size: 1.8rem;
  }

  .faq-question h3 {
    font-size: 1rem;
  }
}