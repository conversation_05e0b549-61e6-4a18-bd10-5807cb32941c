import { Component, OnInit } from '@angular/core';
import { CommonModule } from '@angular/common';
import { RouterModule, ActivatedRoute } from '@angular/router';
import { MaterialModule } from '../../../material/material.module';
import { HeaderComponent } from '../../../components/header/header.component';
import { FooterComponent } from '../../../components/footer/footer.component';
import { BlogPost } from '../../../models/blog.model';
import { BlogService } from '../../../services/blog.service';

@Component({
  selector: 'app-blog-detail',
  standalone: true,
  imports: [
    CommonModule,
    RouterModule,
    MaterialModule,
    HeaderComponent,
    FooterComponent,
  ],
  templateUrl: './blog-detail.component.html',
  styleUrls: ['./blog-detail.component.scss'],
})
export class BlogDetailComponent implements OnInit {
  post: BlogPost | null = null;
  relatedPosts: BlogPost[] = [];
  isLoading = true;

  constructor(
    private route: ActivatedRoute,
    private blogService: BlogService
  ) {}

  ngOnInit(): void {
    this.route.params.subscribe((params) => {
      const slug = params['slug'];
      this.loadPost(slug);
    });
  }

  loadPost(slug: string): void {
    this.isLoading = true;

    this.blogService.getPostBySlug(slug).subscribe({
      next: (post: BlogPost) => {
        this.post = post;
        this.loadRelatedPosts(post.category);
        this.isLoading = false;
      },
      error: () => {
        this.isLoading = false;
        // Handle error (e.g., post not found)
      },
    });
  }

  loadRelatedPosts(category: string): void {
    this.blogService.getPosts().subscribe((posts: BlogPost[]) => {
      // Filter posts by category and exclude current post
      this.relatedPosts = posts
        .filter((p: BlogPost) => p.category === category && p.id !== this.post?.id)
        .slice(0, 3);
    });
  }

  shareOnFacebook(): void {
    const url = window.location.href;
    window.open(
      `https://www.facebook.com/sharer/sharer.php?u=${encodeURIComponent(url)}`,
      '_blank'
    );
  }

  shareOnTwitter(): void {
    const url = window.location.href;
    const text = this.post?.title || 'Check out this article';
    window.open(
      `https://twitter.com/intent/tweet?url=${encodeURIComponent(
        url
      )}&text=${encodeURIComponent(text)}`,
      '_blank'
    );
  }

  shareOnLinkedIn(): void {
    const url = window.location.href;
    const title = this.post?.title || '';
    window.open(
      `https://www.linkedin.com/sharing/share-offsite/?url=${encodeURIComponent(url)}&title=${encodeURIComponent(title)}`,
      '_blank'
    );
  }
}
