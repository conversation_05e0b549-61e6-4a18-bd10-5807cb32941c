import { Component, OnInit } from '@angular/core';
import { CommonModule } from '@angular/common';
import { RouterModule } from '@angular/router';
import { MaterialModule } from '../../material/material.module';
import { FormsModule, ReactiveFormsModule } from '@angular/forms';
import { ProductService } from '../../services/product.service';

interface SoundSignature {
  id: string;
  name: string;
  description: string;
  icon: string;
  characteristics: string[];
  genres: string[];
  examples: { name: string; path: string; image: string }[];
}

@Component({
  selector: 'app-sound-signature',
  standalone: true,
  imports: [
    CommonModule,
    RouterModule,
    MaterialModule,
    FormsModule,
    ReactiveFormsModule
  ],
  templateUrl: './sound-signature.component.html',
  styleUrls: ['./sound-signature.component.scss']
})
export class SoundSignatureComponent implements OnInit {
  selectedSignature: SoundSignature | null = null;

  soundSignatures: SoundSignature[] = [
    {
      id: 'v-shaped',
      name: 'V-Shaped',
      icon: '🌶️',
      description: 'V-shaped sound signatures emphasize both bass and treble, creating a "V" shape in the frequency response. This creates an exciting, energetic sound with punchy bass and crisp highs, but may slightly recede vocals and midrange instruments.',
      characteristics: [
        'Enhanced bass response',
        'Boosted high frequencies',
        'Slightly recessed midrange',
        'Exciting and energetic presentation',
        'Wide perceived soundstage'
      ],
      genres: [
        'EDM',
        'Hip-Hop',
        'Pop',
        'Rock',
        'Metal'
      ],
      examples: [
        { name: 'KZ ZSN Pro X', path: '/products/kz-zsn-pro-x', image: 'https://images.unsplash.com/photo-1590658268037-6bf12165a8df?w=300&h=300&fit=crop' },
        { name: 'Sony WH-1000XM4', path: '/products/sony-wh-1000xm4', image: 'https://images.unsplash.com/photo-1505740420928-5e560c06d30e?w=300&h=300&fit=crop' },
        { name: 'Beyerdynamic DT 990 Pro', path: '/products/beyerdynamic-dt-990-pro', image: 'https://images.unsplash.com/photo-1546435770-a3e426bf472b?w=300&h=300&fit=crop' }
      ]
    },
    {
      id: 'neutral',
      name: 'Neutral/Balanced',
      icon: '🔍',
      description: 'Neutral sound signatures aim to reproduce audio as faithfully as possible to the original recording, without emphasizing any particular frequency range. This results in a balanced, accurate presentation ideal for critical listening and audio production.',
      characteristics: [
        'Even frequency response across the spectrum',
        'Accurate tonal balance',
        'Natural timbre reproduction',
        'Precise imaging',
        'Transparent sound presentation'
      ],
      genres: [
        'Classical',
        'Jazz',
        'Acoustic',
        'Vocal-centric music',
        'Audiophile recordings'
      ],
      examples: [
        { name: 'Sennheiser HD 600', path: '/products/sennheiser-hd-600', image: 'https://images.unsplash.com/photo-1546435770-a3e426bf472b?w=300&h=300&fit=crop' },
        { name: 'AKG K371', path: '/products/akg-k371', image: '//www.headphonezone.in/cdn/shop/products/Headphone-Zone-AKG-K371-1160-1160-1.jpg?v=1593433771' },
        { name: 'Moondrop Aria', path: '/products/moondrop-aria', image: '//www.headphonezone.in/cdn/shop/files/Headphone-Zone-Moondrop-Aria-Snow-Edition-01.jpg?v=1686393671' }
      ]
    },
    {
      id: 'warm',
      name: 'Warm/Smooth',
      icon: '🍰',
      description: 'Warm sound signatures feature a slight emphasis on lower frequencies and a smooth treble response. This creates a rich, full-bodied sound that is easy to listen to for long periods, with excellent vocal reproduction and a natural timbre.',
      characteristics: [
        'Slightly elevated bass and lower midrange',
        'Full-bodied sound',
        'Smooth treble (no harshness)',
        'Rich vocal reproduction',
        'Relaxed presentation'
      ],
      genres: [
        'Vocal Jazz',
        'Soul',
        'R&B',
        'Folk',
        'Acoustic recordings'
      ],
      examples: [
        { name: 'Meze 99 Classics', path: '/products/meze-99-classics', image: 'https://images.unsplash.com/photo-1505740420928-5e560c06d30e?w=300&h=300&fit=crop' },
        { name: 'Audeze LCD-2', path: '/products/audeze-lcd-2', image: '//www.headphonezone.in/cdn/shop/products/Headphone-Zone-Audeze-LCD-2-Classic-1160-1160-1.jpg?v=1592323493' },
        { name: 'FiiO FH3', path: '/products/fiio-fh3', image: '//www.headphonezone.in/cdn/shop/products/Headphone-Zone-FiiO-FH3-1160-1160-Black_1.jpg?v=1597308507' }
      ]
    },
    {
      id: 'bright',
      name: 'Bright/Analytical',
      icon: '🍿',
      description: 'Bright sound signatures emphasize the upper midrange and treble frequencies, resulting in a detailed, clear sound with excellent instrument separation. This signature excels at revealing subtle details in music but may sound fatiguing during long listening sessions.',
      characteristics: [
        'Enhanced upper midrange and treble',
        'Highly detailed presentation',
        'Excellent clarity and instrument separation',
        'Precise imaging',
        'Revealing of recording flaws'
      ],
      genres: [
        'Classical',
        'Jazz',
        'Acoustic',
        'Well-recorded audiophile tracks',
        'Live recordings'
      ],
      examples: [
        { name: 'Grado SR325x', path: '/products/grado-sr325x', image: '//www.headphonezone.in/cdn/shop/products/Headphone-Zone-Grado-SR325x-1160-1160-01.jpg?v=1626345012' },
        { name: 'Audio-Technica ATH-M50x', path: '/products/audio-technica-ath-m50x', image: '//www.headphonezone.in/cdn/shop/products/audio-technica-ath-m50x-headphone-zone-13980464201791.jpg?v=1579705237' },
        { name: 'Etymotic ER2SE', path: '/products/etymotic-er2se', image: '//www.headphonezone.in/cdn/shop/products/Headphone-Zone-Etymotic-ER2SE-1160-1160-1.jpg?v=1593433771' }
      ]
    },
    {
      id: 'bass',
      name: 'Bass-Heavy',
      icon: '💣',
      description: 'Bass-heavy sound signatures feature a significant emphasis on low frequencies, creating a powerful, impactful sound with deep, resonant bass. This signature is perfect for bass-centric music genres and for listeners who enjoy a fun, energetic presentation.',
      characteristics: [
        'Pronounced bass response',
        'Deep sub-bass extension',
        'Powerful and impactful sound',
        'Warm lower midrange',
        'Immersive listening experience'
      ],
      genres: [
        'EDM',
        'Hip-Hop',
        'Dubstep',
        'R&B',
        'Modern Pop'
      ],
      examples: [
        { name: 'Sony WF-1000XM4', path: '/products/sony-wf-1000xm4', image: 'https://images.unsplash.com/photo-1590658268037-6bf12165a8df?w=300&h=300&fit=crop' },
        { name: 'Skullcandy Crusher Evo', path: '/products/skullcandy-crusher-evo', image: '//www.headphonezone.in/cdn/shop/products/Headphone-Zone-Skullcandy-Crusher-Evo-True-Black-1160-1160-01.jpg?v=1614070725' },
        { name: 'BLON BL-03', path: '/products/blon-bl-03', image: '//www.headphonezone.in/cdn/shop/products/Headphone-Zone-BLON-BL-03-1160-1160-Silver.jpg?v=1593433771' }
      ]
    },
    {
      id: 'fun',
      name: 'Fun/Energetic',
      icon: '🎉',
      description: 'Fun sound signatures prioritize an engaging, lively presentation over strict accuracy. They typically feature enhanced bass and treble with a forward midrange, creating an exciting sound that makes music feel more dynamic and immersive.',
      characteristics: [
        'Elevated bass with good impact',
        'Forward, present midrange',
        'Sparkly treble',
        'Dynamic, engaging presentation',
        'Lively character'
      ],
      genres: [
        'Pop',
        'Rock',
        'Electronic',
        'Hip-Hop',
        'Modern productions'
      ],
      examples: [
        { name: 'Campfire Audio Honeydew', path: '/products/campfire-audio-honeydew', image: 'https://images.unsplash.com/photo-1484704849700-f032a568e944?w=300&h=300&fit=crop' },
        { name: 'V-MODA Crossfade M-100', path: '/products/v-moda-crossfade-m-100', image: '//www.headphonezone.in/cdn/shop/products/v-moda-crossfade-m-100-headphone-zone-29093444747.jpg?v=1579705237' },
        { name: 'FiiO FH5', path: '/products/fiio-fh5', image: '//www.headphonezone.in/cdn/shop/products/Headphone-Zone-FiiO-FH5-1160-1160-Black.jpg?v=1592323493' }
      ]
    }
  ];

  constructor(private productService: ProductService) {}

  ngOnInit(): void {
    // Set default selected signature
    this.selectedSignature = this.soundSignatures[0];
  }

  selectSignature(signature: SoundSignature): void {
    this.selectedSignature = signature;
  }
}
