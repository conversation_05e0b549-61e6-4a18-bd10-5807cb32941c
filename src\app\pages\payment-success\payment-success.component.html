<div class="payment-success min-h-screen bg-gray-50 py-12">
    <div class="container mx-auto px-4">
        <!-- Success Message -->
        <div class="text-center mb-12" @fadeIn>
            <div class="success-checkmark mb-6">
                <mat-icon class="text-6xl text-success">check_circle</mat-icon>
            </div>
            <h1 class="text-3xl font-bold text-gray-900 mb-2">Payment Successful!</h1>
            <p class="text-gray-600">Thank you for your purchase. Your order has been confirmed.</p>
        </div>

        <!-- Order Details -->
        <div class="max-w-3xl mx-auto bg-white rounded-lg shadow-md p-8 mb-8" @fadeIn>
            <div class="grid grid-cols-1 md:grid-cols-2 gap-8">
                <!-- Order Info -->
                <div>
                    <h2 class="text-lg font-semibold mb-4">Order Information</h2>
                    <div class="space-y-3">
                        <div class="flex items-center justify-between">
                            <span class="text-gray-600">Order Number</span>
                            <div class="flex items-center">
                                <span class="font-mono">{{ orderNumber }}</span>
                                <button mat-icon-button (click)="copyOrderNumber()" matTooltip="Copy order number"
                                    class="ml-2">
                                    <mat-icon>content_copy</mat-icon>
                                </button>
                            </div>
                        </div>
                        <div class="flex items-center justify-between">
                            <span class="text-gray-600">Order Amount</span>
                            <span class="font-semibold">${{ orderAmount.toFixed(2) }}</span>
                        </div>
                        <div class="flex items-center justify-between">
                            <span class="text-gray-600">Payment Status</span>
                            <span class="inline-flex items-center text-success">
                                <mat-icon class="text-sm mr-1">check_circle</mat-icon>
                                Paid
                            </span>
                        </div>
                    </div>
                </div>

                <!-- Delivery Info -->
                <div>
                    <h2 class="text-lg font-semibold mb-4">Delivery Information</h2>
                    <div class="space-y-3">
                        <div class="flex items-start">
                            <mat-icon class="text-gray-400 mr-2">local_shipping</mat-icon>
                            <div>
                                <p class="font-medium">Estimated Delivery</p>
                                <p class="text-gray-600">{{ formatDate(estimatedDelivery) }}</p>
                            </div>
                        </div>
                        <div class="flex items-start">
                            <mat-icon class="text-gray-400 mr-2">location_on</mat-icon>
                            <div>
                                <p class="font-medium">Shipping Address</p>
                                <p class="text-gray-600">Your shipping address will be displayed here</p>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Next Steps -->
        <div class="max-w-3xl mx-auto" @listAnimation>
            <h2 class="text-2xl font-semibold text-center mb-8">What's Next?</h2>
            <div class="grid grid-cols-1 md:grid-cols-3 gap-6">
                <div *ngFor="let step of nextSteps"
                    class="next-step-card bg-white rounded-lg shadow-md p-6 text-center">
                    <mat-icon class="text-4xl text-primary mb-4">{{ step.icon }}</mat-icon>
                    <h3 class="font-semibold mb-2">{{ step.title }}</h3>
                    <p class="text-gray-600 text-sm mb-4">{{ step.description }}</p>
                    <a [routerLink]="step.link" mat-stroked-button color="primary">
                        {{ step.action }}
                    </a>
                </div>
            </div>
        </div>

        <!-- Return to Home -->
        <div class="text-center mt-12">
            <a routerLink="/" mat-raised-button color="primary">
                Return to Homepage
            </a>
        </div>
    </div>
</div>