<div class="checkout-page py-8">
  <div class="container mx-auto px-4">
    <!-- Breadcrumb Navigation -->
    <div class="breadcrumb mb-6">
      <ul class="flex items-center text-sm">
        <li class="flex items-center">
          <a href="/" class="text-gray-500 hover:text-primary">Home</a>
          <span class="mx-2 text-gray-400">/</span>
        </li>
        <li class="flex items-center">
          <a href="/cart" class="text-gray-500 hover:text-primary">Cart</a>
          <span class="mx-2 text-gray-400">/</span>
        </li>
        <li class="text-primary font-medium">Checkout</li>
      </ul>
    </div>

    <!-- Page Title -->
    <h1 class="text-2xl font-semibold mb-6 text-center">Checkout</h1>

    <div class="flex flex-col lg:flex-row gap-8">
      <!-- Left Column - Form -->
      <div class="flex-1">
        <form [formGroup]="checkoutForm" (ngSubmit)="checkout()" class="space-y-6">
          <!-- Personal Information -->
          <div formGroupName="personalInfo" class="form-section">
            <h2 class="section-header">Contact Information</h2>
            <div class="space-y-4">
              <mat-form-field appearance="outline" class="w-full">
                <mat-label>Email</mat-label>
                <input matInput type="email" formControlName="email" placeholder="<EMAIL>">
                <mat-error *ngIf="getFormError('personalInfo.email', 'required')">
                  Email is required
                </mat-error>
                <mat-error *ngIf="getFormError('personalInfo.email', 'email')">
                  Please enter a valid email address
                </mat-error>
              </mat-form-field>

              <mat-form-field appearance="outline" class="w-full">
                <mat-label>Phone</mat-label>
                <input matInput type="tel" formControlName="phone" placeholder="+****************">
                <mat-error *ngIf="getFormError('personalInfo.phone', 'required')">
                  Phone number is required
                </mat-error>
                <mat-error *ngIf="getFormError('personalInfo.phone', 'pattern')">
                  Please enter a valid phone number
                </mat-error>
              </mat-form-field>
            </div>
          </div>

          <!-- Shipping Address -->
          <div formGroupName="shippingAddress" class="form-section">
            <h2 class="section-header">Shipping Address</h2>
            <div class="space-y-4">
              <mat-form-field appearance="outline" class="w-full">
                <mat-label>Full Name</mat-label>
                <input matInput formControlName="fullName" placeholder="John Doe">
                <mat-error *ngIf="getFormError('shippingAddress.fullName', 'required')">
                  Full name is required
                </mat-error>
              </mat-form-field>

              <mat-form-field appearance="outline" class="w-full">
                <mat-label>Address Line 1</mat-label>
                <input matInput formControlName="addressLine1" placeholder="Street address">
                <mat-error *ngIf="getFormError('shippingAddress.addressLine1', 'required')">
                  Address is required
                </mat-error>
              </mat-form-field>

              <mat-form-field appearance="outline" class="w-full">
                <mat-label>Address Line 2</mat-label>
                <input matInput formControlName="addressLine2" placeholder="Apt, Suite, Unit, etc. (optional)">
              </mat-form-field>

              <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
                <mat-form-field appearance="outline">
                  <mat-label>City</mat-label>
                  <input matInput formControlName="city">
                  <mat-error *ngIf="getFormError('shippingAddress.city', 'required')">
                    City is required
                  </mat-error>
                </mat-form-field>

                <mat-form-field appearance="outline">
                  <mat-label>State</mat-label>
                  <input matInput formControlName="state">
                  <mat-error *ngIf="getFormError('shippingAddress.state', 'required')">
                    State is required
                  </mat-error>
                </mat-form-field>
              </div>

              <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
                <mat-form-field appearance="outline">
                  <mat-label>ZIP Code</mat-label>
                  <input matInput formControlName="zipCode" placeholder="12345">
                  <mat-error *ngIf="getFormError('shippingAddress.zipCode', 'required')">
                    ZIP code is required
                  </mat-error>
                  <mat-error *ngIf="getFormError('shippingAddress.zipCode', 'pattern')">
                    Please enter a valid ZIP code
                  </mat-error>
                </mat-form-field>

                <mat-form-field appearance="outline">
                  <mat-label>Country</mat-label>
                  <mat-select formControlName="country">
                    <mat-option value="US">United States</mat-option>
                    <mat-option value="CA">Canada</mat-option>
                    <mat-option value="IN">India</mat-option>
                    <mat-option value="UK">United Kingdom</mat-option>
                    <mat-option value="AU">Australia</mat-option>
                  </mat-select>
                  <mat-error *ngIf="getFormError('shippingAddress.country', 'required')">
                    Country is required
                  </mat-error>
                </mat-form-field>
              </div>
            </div>
          </div>

          <!-- Payment Method -->
          <div class="form-section">
            <h2 class="section-header">Payment Method</h2>
            <div class="grid grid-cols-1 md:grid-cols-3 gap-4">
              <mat-card *ngFor="let method of paymentMethods" class="payment-method-card"
                [class.selected]="checkoutForm.get('paymentMethod')?.value === method.id"
                (click)="checkoutForm.get('paymentMethod')?.setValue(method.id)">
                <mat-card-content class="flex flex-col items-center text-center">
                  <mat-icon>{{ method.icon }}</mat-icon>
                  <h3>{{ method.name }}</h3>
                  <p>{{ method.description }}</p>
                </mat-card-content>
              </mat-card>
            </div>

            <!-- Payment Instructions -->
            <div class="mt-4 pt-4 border-t border-gray-100" *ngIf="checkoutForm.get('paymentMethod')?.value">
              <div [ngSwitch]="checkoutForm.get('paymentMethod')?.value">
                <div *ngSwitchCase="'credit-card'" class="text-sm text-gray-600">
                  <p>Your card will be charged after order confirmation.</p>
                </div>
                <div *ngSwitchCase="'upi'" class="text-sm text-gray-600">
                  <p>You'll be redirected to complete the payment using your preferred UPI app.</p>
                </div>
                <div *ngSwitchCase="'paypal'" class="text-sm text-gray-600">
                  <p>You'll be redirected to PayPal to complete your purchase securely.</p>
                </div>
                <div *ngSwitchCase="'net-banking'" class="text-sm text-gray-600">
                  <p>You'll be redirected to your bank's website to complete the payment.</p>
                </div>
                <div *ngSwitchCase="'emi'" class="text-sm text-gray-600">
                  <p>Select your bank and EMI plan on the next screen.</p>
                </div>
                <div *ngSwitchCase="'cod'" class="text-sm text-gray-600">
                  <p>Pay in cash when your order is delivered. Additional fee may apply.</p>
                </div>
              </div>
            </div>
          </div>

          <!-- Save Information -->
          <div class="form-section">
            <mat-checkbox formControlName="saveInfo" color="primary" class="text-gray-700">
              Save this information for next time
            </mat-checkbox>

            <!-- Trust Badges -->
            <div class="mt-6 border-t pt-4">
              <p class="text-sm text-gray-500 mb-3">Secure Checkout</p>
              <div class="flex items-center space-x-4">
                <img src="https://cdn.shopify.com/s/files/1/0153/8863/files/visa-card.svg" alt="Visa" class="h-6">
                <img src="https://cdn.shopify.com/s/files/1/0153/8863/files/master-card.svg" alt="Mastercard" class="h-6">
                <img src="https://cdn.shopify.com/s/files/1/0153/8863/files/american-express.svg" alt="American Express" class="h-6">
                <img src="https://cdn.shopify.com/s/files/1/0153/8863/files/paypal.svg" alt="PayPal" class="h-6">
              </div>
              <div class="flex items-center mt-3">
                <mat-icon class="text-green-600 mr-2">lock</mat-icon>
                <span class="text-xs text-gray-600">Your payment information is secure and encrypted</span>
              </div>
            </div>
          </div>
        </form>
      </div>

      <!-- Right Column - Order Summary -->
      <div class="lg:w-96">
        <div class="order-summary sticky top-24">
          <h2 class="section-header">Order Summary</h2>

          <!-- Cart Items -->
          <div class="space-y-4 mb-6" [@itemAnimation]="(cartItems$ | async)?.length">
            <div *ngFor="let item of cartItems$ | async" class="flex items-center gap-4 pb-4 border-b border-gray-100">
              <img [src]="item.product.imageUrl" [alt]="item.product.name" class="w-16 h-16 object-cover rounded">
              <div class="flex-1">
                <h3 class="font-medium text-sm">{{ item.product.name }}</h3>
                <div class="flex items-center mt-1">
                  <mat-form-field appearance="outline" class="w-20">
                    <input matInput type="number" [value]="item.quantity" min="0"
                      (change)="updateQuantity(item.product.id, $any($event.target).value)">
                  </mat-form-field>
                  <button mat-icon-button color="warn" (click)="removeItem(item.product.id)" class="ml-1">
                    <mat-icon>delete</mat-icon>
                  </button>
                </div>
              </div>
              <span class="font-medium">${{ (item.product.price * item.quantity).toFixed(2) }}</span>
            </div>
          </div>

          <!-- Order Details -->
          <ng-container *ngIf="orderSummary$ | async as summary">
            <div class="border-t pt-4 space-y-3">
              <div class="flex justify-between">
                <span>Subtotal</span>
                <span>${{ summary.subtotal.toFixed(2) }}</span>
              </div>
              <div class="flex justify-between">
                <span>Shipping</span>
                <span>${{ summary.shipping.toFixed(2) }}</span>
              </div>
              <div class="flex justify-between">
                <span>Tax</span>
                <span>${{ summary.tax.toFixed(2) }}</span>
              </div>
              <div class="flex justify-between total-row border-t pt-3 mt-3">
                <span>Total</span>
                <span>${{ summary.total.toFixed(2) }}</span>
              </div>
            </div>

            <!-- Checkout Button -->
            <button mat-raised-button color="primary" class="btn-primary mt-6"
              [disabled]="checkoutForm.invalid || isProcessing" (click)="checkout()">
              <ng-container *ngIf="!isProcessing && !isPaymentRedirecting">
                PLACE ORDER
              </ng-container>
              <ng-container *ngIf="isProcessing">
                <mat-spinner diameter="24" class="inline-block mr-2"></mat-spinner>
                PROCESSING...
              </ng-container>
              <ng-container *ngIf="!isProcessing && isPaymentRedirecting">
                <mat-spinner diameter="24" class="inline-block mr-2"></mat-spinner>
                REDIRECTING...
              </ng-container>
            </button>

            <!-- Shipping Policy Note -->
            <p class="text-xs text-gray-500 mt-4 text-center">
              By placing your order, you agree to our <a href="#" class="text-primary hover:underline">Terms of Service</a> and <a href="#" class="text-primary hover:underline">Privacy Policy</a>
            </p>
          </ng-container>
        </div>
      </div>
    </div>
  </div>
</div>