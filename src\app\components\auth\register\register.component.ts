import { Component } from '@angular/core';
import { CommonModule } from '@angular/common';
import { RouterModule } from '@angular/router';
import { MaterialModule } from '../../../material/material.module';
import { FormsModule, ReactiveFormsModule, FormBuilder, FormGroup, Validators } from '@angular/forms';
import { AuthService } from '../../../services/auth.service';
import { ToastService } from '../../../services/toast.service';

@Component({
  selector: 'app-register',
  standalone: true,
  imports: [
    CommonModule,
    RouterModule,
    MaterialModule,
    FormsModule,
    ReactiveFormsModule
  ],
  templateUrl: './register.component.html',
  styleUrls: ['./register.component.scss']
})
export class RegisterComponent {
  registerForm: FormGroup;
  hidePassword = true;
  isLoading = false;

  constructor(
    private fb: FormBuilder,
    private authService: AuthService,
    private toastService: ToastService
  ) {
    this.registerForm = this.fb.group({
      firstName: ['', [Validators.required]],
      lastName: ['', [Validators.required]],
      email: ['', [Validators.required, Validators.email]],
      password: ['', [Validators.required, Validators.minLength(6)]],
      acceptTerms: [false, [Validators.requiredTrue]]
    });
  }

  onSubmit(): void {
    if (this.registerForm.valid) {
      this.isLoading = true;
      const { firstName, lastName, email, password } = this.registerForm.value;
      
      // Simulate API call
      setTimeout(() => {
        this.authService.register(firstName, lastName, email, password).subscribe({
          next: () => {
            this.toastService.showSuccess('Registration successful');
            // Navigate to login page
          },
          error: (error) => {
            this.toastService.showError(error || 'Registration failed. Please try again.');
            this.isLoading = false;
          },
          complete: () => {
            this.isLoading = false;
          }
        });
      }, 1000);
    } else {
      this.registerForm.markAllAsTouched();
    }
  }

  getErrorMessage(controlName: string): string {
    const control = this.registerForm.get(controlName);
    
    if (!control) return '';
    
    if (control.hasError('required')) {
      return 'This field is required';
    }
    
    if (controlName === 'email' && control.hasError('email')) {
      return 'Please enter a valid email address';
    }
    
    if (controlName === 'password' && control.hasError('minlength')) {
      return 'Password must be at least 6 characters long';
    }
    
    if (controlName === 'acceptTerms' && control.hasError('requiredTrue')) {
      return 'You must accept the terms and conditions';
    }
    
    return '';
  }
}
