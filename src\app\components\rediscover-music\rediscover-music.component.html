<section class="rediscover-section">
  <div class="rediscover-container">
    <div class="rediscover-slider">
      <div
        *ngFor="let slide of slides; let i = index"
        class="rediscover-slide"
        [class.active]="i === currentSlideIndex"
        [style.background-image]="'url(' + slide.backgroundImage + ')'"
        [@fadeAnimation]="i === currentSlideIndex ? 'visible' : 'hidden'"
      >
        <div class="slide-content">
          <h3 class="artist-name">{{ slide.artist }}</h3>
          <h2 class="slide-title">{{ slide.title }}</h2>
          <p class="slide-description">{{ slide.description }}</p>
          <p class="slide-note"><strong>P.S. When you listen to {{ slide.artist }}, <em>never</em> do it on crappy sounding headphones.</strong></p>
          <a routerLink="/products" class="shop-button">Shop Now</a>
        </div>
      </div>
    </div>

    <div class="slider-controls">
      <button class="prev-button" (click)="prevSlide()">
        <span class="material-icons">arrow_back</span>
      </button>
      <div class="slider-dots">
        <span
          *ngFor="let slide of slides; let i = index"
          class="dot"
          [class.active]="i === currentSlideIndex"
          (click)="setCurrentSlide(i)"
        ></span>
      </div>
      <button class="next-button" (click)="nextSlide()">
        <span class="material-icons">arrow_forward</span>
      </button>
    </div>
  </div>
</section>
