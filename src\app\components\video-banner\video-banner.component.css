.video-banner-section {
  background-color: #ffffff;
  padding: 3rem 0;
}

.media-container {
  aspect-ratio: 1 / 1;
  max-height: 1500px;
  border-radius: 0.5rem;
  overflow: hidden;
  position: relative;
  cursor: pointer;
  transition: transform 0.3s ease, box-shadow 0.3s ease;
}

.media-container:hover {
  transform: translateY(-5px);
  box-shadow: 0 10px 20px rgba(0, 0, 0, 0.1);
}

.media-container img,
.media-container video {
  width: 100%;
  height: 100%;
  object-fit: cover;
  object-position: center;
}

h2 {
  color: #333;
  line-height: 1.2;
  font-weight: 700;
}

p {
  color: #4b5563;
  line-height: 1.6;
}

.journey-banner {
  background-color: #f9f9f9;
  border-top: 1px solid #e5e7eb;
  border-bottom: 1px solid #e5e7eb;
  padding: 1.5rem 0;
}

.journey-banner h3 {
  color: #333;
  font-weight: 600;
}

/* Responsive styles */
@media (max-width: 768px) {
  .video-banner-section {
    padding-top: 2rem;
    padding-bottom: 2rem;
  }

  .media-container {
    margin-bottom: 2rem;
    max-height: 350px;
  }

  h2 {
    font-size: 1.75rem;
  }
}