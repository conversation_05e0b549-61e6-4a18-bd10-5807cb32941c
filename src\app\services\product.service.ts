import { Injectable } from '@angular/core';
import { BehaviorSubject, Observable, of, timer } from 'rxjs';
import { map, delay, tap } from 'rxjs/operators';
import { Product, FeaturedDeal } from '../models/product.model';
import { ToastService } from './toast.service';

@Injectable({
  providedIn: 'root',
})
export class ProductService {
  private products = new BehaviorSubject<Product[]>([
    {
      id: 1,
      name: 'Headphone Zone x Oriveti Blackbird',
      description: 'Audiophile-grade IEMs with exceptional sound quality',
      price: 12999,
      discountedPrice: 9999,
      imageUrl: 'https://images.unsplash.com/photo-1590658268037-6bf12165a8df?w=450&h=450&fit=crop',
      category: 'In-Ear Monitors',
      rating: 4.8,
      reviewCount: 128,
      stock: 50,
      isHot: true,
      soundSignature: 'balanced',
      badges: ['staff-pick', 'limited-edition'],
      technicalSpecs: {
        driverType: 'Hybrid (1 Dynamic + 2 Balanced Armature)',
        driverSize: '10mm Dynamic Driver',
        impedance: '32 Ohms',
        sensitivity: '108dB/mW',
        frequency: '20Hz - 20kHz',
        cableLength: '1.2m',
        weight: '8g (without cable)',
        connector: 'MMCX'
      }
    },
    {
      id: 2,
      name: 'Headphone Zone x FiiO JD1',
      description: 'Affordable audiophile IEMs with balanced sound signature',
      price: 3999,
      discountedPrice: 2999,
      imageUrl: 'https://images.unsplash.com/photo-1583394838336-acd977736f90?w=450&h=450&fit=crop',
      category: 'In-Ear Monitors',
      rating: 4.6,
      reviewCount: 95,
      stock: 30,
      isNew: true,
      soundSignature: 'warm',
      badges: ['value-for-money', 'ideal-for-beginners'],
      technicalSpecs: {
        driverType: 'Single Dynamic Driver',
        driverSize: '10mm',
        impedance: '32 Ohms',
        sensitivity: '106dB/mW',
        frequency: '20Hz - 20kHz',
        cableLength: '1.2m',
        weight: '5g (without cable)',
        connector: '3.5mm Gold Plated'
      }
    },
    {
      id: 3,
      name: 'Headphone Zone x Tangzu Wan\'er S.G',
      description: 'Special edition IEMs with stunning design and sound',
      price: 5999,
      discountedPrice: 4999,
      imageUrl: 'https://images.unsplash.com/photo-1484704849700-f032a568e944?w=450&h=450&fit=crop',
      category: 'In-Ear Monitors',
      rating: 4.7,
      reviewCount: 112,
      stock: 25,
      isHot: true,
      soundSignature: 'v-shaped',
      badges: ['best-seller', 'limited-edition'],
      technicalSpecs: {
        driverType: 'Single Dynamic Driver',
        driverSize: '10mm LCP Diaphragm',
        impedance: '32 Ohms',
        sensitivity: '110dB/mW',
        frequency: '20Hz - 20kHz',
        cableLength: '1.2m',
        weight: '6g (without cable)',
        connector: '0.78mm 2-Pin'
      }
    },
    {
      id: 4,
      name: 'Headphone Zone x Oriveti OD200',
      description: 'Premium over-ear headphones with exceptional clarity',
      price: 19999,
      discountedPrice: 16999,
      imageUrl: 'https://images.unsplash.com/photo-1505740420928-5e560c06d30e?w=450&h=450&fit=crop',
      category: 'Over-Ear Headphones',
      rating: 4.9,
      reviewCount: 78,
      stock: 15,
      isNew: true,
      soundSignature: 'neutral',
      badges: ['award-winner', 'staff-pick'],
      technicalSpecs: {
        driverType: 'Planar Magnetic',
        driverSize: '50mm',
        impedance: '32 Ohms',
        sensitivity: '100dB/mW',
        frequency: '10Hz - 40kHz',
        cableLength: '1.5m',
        weight: '320g',
        connector: '3.5mm with 6.3mm adapter'
      }
    },
    {
      id: 5,
      name: 'Moondrop Aria',
      description: 'Harman-tuned IEMs with exceptional value',
      price: 7999,
      discountedPrice: 6999,
      imageUrl: 'https://images.unsplash.com/photo-1612444530582-fc66183b16f7?w=450&h=450&fit=crop',
      category: 'In-Ear Monitors',
      rating: 4.7,
      reviewCount: 245,
      stock: 40,
      soundSignature: 'balanced',
      badges: ['value-for-money', 'best-seller'],
      technicalSpecs: {
        driverType: 'Single Dynamic Driver',
        driverSize: '10mm LCP Diaphragm',
        impedance: '32 Ohms',
        sensitivity: '122dB/mW',
        frequency: '5Hz - 36kHz',
        cableLength: '1.2m',
        weight: '5.7g (without cable)',
        connector: '0.78mm 2-Pin'
      }
    },
    {
      id: 6,
      name: 'Sennheiser HD 560S',
      description: 'Reference-grade open-back headphones for critical listening',
      price: 18990,
      discountedPrice: 16990,
      imageUrl: 'https://images.unsplash.com/photo-1546435770-a3e426bf472b?w=450&h=450&fit=crop',
      category: 'Over-Ear Headphones',
      rating: 4.8,
      reviewCount: 156,
      stock: 20,
      soundSignature: 'neutral',
      badges: ['ideal-for-beginners', 'award-winner'],
      technicalSpecs: {
        driverType: 'Dynamic',
        driverSize: '38mm',
        impedance: '120 Ohms',
        sensitivity: '110dB/mW',
        frequency: '6Hz - 38kHz',
        cableLength: '3m',
        weight: '240g',
        connector: '6.3mm with 3.5mm adapter'
      }
    }
  ]);

  private realTimeViewers = new Map<number, number>();
  private stockUpdateInterval: any;

  constructor(private toastService: ToastService) {
    this.initializeRealTimeViewers();
    this.startStockUpdateSimulation();
  }

  getProducts(): Observable<Product[]> {
    return this.products.asObservable();
  }

  getFeaturedDeals(): Observable<FeaturedDeal[]> {
    return this.products.pipe(
      map((products) => {
        return products
          .filter((p) => p.discountedPrice)
          .map((p) => ({
            ...p,
            endsAt: this.generateRandomEndDate(),
            progress: Math.random() * 100,
            originalPrice: p.price,
            soldCount: Math.floor(Math.random() * 100),
          }));
      })
    );
  }

  getProductById(id: number): Observable<Product | undefined> {
    return this.products.pipe(
      map((products) => products.find((p) => p.id === id))
    );
  }

  getRelatedProducts(category: string, currentProductId: number): Observable<Product[]> {
    return this.products.pipe(
      map((products) =>
        products
          .filter(p => p.category === category && p.id !== currentProductId)
          .slice(0, 4)
      )
    );
  }

  searchProducts(query: string): Observable<Product[]> {
    return this.products.pipe(
      map((products) =>
        products.filter(
          (p) =>
            p.name.toLowerCase().includes(query.toLowerCase()) ||
            p.description.toLowerCase().includes(query.toLowerCase())
        )
      ),
      delay(300) // Simulate network delay
    );
  }

  getSearchSuggestions(query: string): Observable<string[]> {
    return this.products.pipe(
      map((products) => {
        const suggestions = new Set<string>();
        products.forEach((p) => {
          if (p.name.toLowerCase().includes(query.toLowerCase())) {
            suggestions.add(p.name);
          }
          if (p.category.toLowerCase().includes(query.toLowerCase())) {
            suggestions.add(p.category);
          }
        });
        return Array.from(suggestions).slice(0, 5);
      }),
      delay(200) // Simulate network delay
    );
  }

  updateStock(productId: number, newStock: number): Observable<void> {
    return new Observable((subscriber) => {
      const products = this.products.value;
      const productIndex = products.findIndex((p) => p.id === productId);

      if (productIndex !== -1) {
        products[productIndex] = {
          ...products[productIndex],
          stock: newStock,
        };

        this.products.next(products);

        // Show warning for low stock
        if (newStock <= 5 && newStock > 0) {
          this.toastService.showStockWarning(
            products[productIndex].name,
            newStock
          );
        }

        subscriber.next();
      }
      subscriber.complete();
    });
  }

  getViewerCount(productId: number): Observable<number> {
    return timer(0, 30000).pipe(
      map(() => this.realTimeViewers.get(productId) || 0)
    );
  }

  private initializeRealTimeViewers(): void {
    this.products.value.forEach((product) => {
      this.realTimeViewers.set(product.id, Math.floor(Math.random() * 50) + 1);
    });

    // Simulate real-time viewer fluctuations
    setInterval(() => {
      this.products.value.forEach((product) => {
        const currentViewers = this.realTimeViewers.get(product.id) || 0;
        const change = Math.floor(Math.random() * 5) - 2; // Random change between -2 and 2
        const newViewers = Math.max(0, currentViewers + change);
        this.realTimeViewers.set(product.id, newViewers);

        // Notify about high viewer counts
        if (newViewers >= 20) {
          this.toastService.showViewersNotification(product.name, newViewers);
        }
      });
    }, 30000);
  }

  private startStockUpdateSimulation(): void {
    // Simulate occasional stock updates
    this.stockUpdateInterval = setInterval(() => {
      const products = this.products.value;
      const randomProduct =
        products[Math.floor(Math.random() * products.length)];
      const stockChange = Math.floor(Math.random() * 3) - 1; // Random change between -1 and 1

      if (randomProduct.stock + stockChange >= 0) {
        this.updateStock(
          randomProduct.id,
          randomProduct.stock + stockChange
        ).subscribe();
      }
    }, 60000);
  }

  private generateRandomEndDate(): Date {
    const date = new Date();
    date.setHours(date.getHours() + Math.floor(Math.random() * 72) + 1);
    return date;
  }

  ngOnDestroy(): void {
    if (this.stockUpdateInterval) {
      clearInterval(this.stockUpdateInterval);
    }
  }
}
