import { Injectable } from '@angular/core';
import { BehaviorSubject, Observable, map } from 'rxjs';
import { Product, CartItem } from '../models/product.model';
import { ToastService } from './toast.service';

@Injectable({
  providedIn: 'root',
})
export class CartService {
  private cartItems = new BehaviorSubject<CartItem[]>([]);
  private stockLevels = new Map<number, BehaviorSubject<number>>();

  constructor(private toastService: ToastService) {
    // Load cart from localStorage
    this.loadCart();
  }

  getCartItems(): Observable<CartItem[]> {
    return this.cartItems.asObservable();
  }

  getCartCount(): Observable<number> {
    return this.cartItems.pipe(
      map((items) => items.reduce((total, item) => total + item.quantity, 0))
    );
  }

  getCartTotal(): Observable<number> {
    return this.cartItems.pipe(
      map((items) =>
        items.reduce(
          (total, item) =>
            total +
            (item.product.discountedPrice || item.product.price) *
              item.quantity,
          0
        )
      )
    );
  }

  getStockLevel(productId: number): Observable<number> {
    if (!this.stockLevels.has(productId)) {
      this.stockLevels.set(productId, new BehaviorSubject<number>(0));
    }
    return this.stockLevels.get(productId)!.asObservable();
  }

  updateStockLevel(productId: number, stock: number): void {
    if (!this.stockLevels.has(productId)) {
      this.stockLevels.set(productId, new BehaviorSubject<number>(stock));
    } else {
      this.stockLevels.get(productId)!.next(stock);
    }

    // Show warning for low stock
    if (stock <= 5 && stock > 0) {
      const cartItem = this.cartItems.value.find(
        (item) => item.product.id === productId
      );
      if (cartItem) {
        this.toastService.showStockWarning(cartItem.product.name, stock);
      }
    }
  }

  addToCart(product: Product, quantity: number = 1): void {
    const currentItems = this.cartItems.value;
    const existingItem = currentItems.find(
      (item) => item.product.id === product.id
    );

    if (existingItem) {
      // Check stock before updating quantity
      const currentStock =
        this.stockLevels.get(product.id)?.value ?? product.stock;
      if (currentStock >= quantity) {
        existingItem.quantity += quantity;
        this.updateStockLevel(product.id, currentStock - quantity);
      } else {
        this.toastService.showError('Sorry, this item is out of stock');
        return;
      }
    } else {
      const currentStock =
        this.stockLevels.get(product.id)?.value ?? product.stock;
      if (currentStock >= quantity) {
        currentItems.push({ product, quantity });
        this.updateStockLevel(product.id, currentStock - quantity);
      } else {
        this.toastService.showError('Sorry, this item is out of stock');
        return;
      }
    }

    this.cartItems.next(currentItems);
    this.saveCart();
  }

  removeFromCart(productId: number): void {
    const currentItems = this.cartItems.value;
    const itemToRemove = currentItems.find(
      (item) => item.product.id === productId
    );

    if (itemToRemove) {
      // Restore stock level
      const currentStock = this.stockLevels.get(productId)?.value ?? 0;
      this.updateStockLevel(productId, currentStock + itemToRemove.quantity);

      // Remove item from cart
      this.cartItems.next(
        currentItems.filter((item) => item.product.id !== productId)
      );
      this.saveCart();
    }
  }

  updateQuantity(productId: number, newQuantity: number): void {
    if (newQuantity < 0) return;

    const currentItems = this.cartItems.value;
    const itemToUpdate = currentItems.find(
      (item) => item.product.id === productId
    );

    if (itemToUpdate) {
      const currentStock =
        this.stockLevels.get(productId)?.value ?? itemToUpdate.product.stock;
      const stockDifference = itemToUpdate.quantity - newQuantity;

      if (newQuantity === 0) {
        this.removeFromCart(productId);
      } else if (stockDifference <= currentStock) {
        // Update stock level
        this.updateStockLevel(productId, currentStock + stockDifference);

        // Update cart quantity
        itemToUpdate.quantity = newQuantity;
        this.cartItems.next(currentItems);
        this.saveCart();
      } else {
        this.toastService.showError('Requested quantity not available');
      }
    }
  }

  clearCart(): void {
    // Restore all stock levels
    this.cartItems.value.forEach((item) => {
      const currentStock = this.stockLevels.get(item.product.id)?.value ?? 0;
      this.updateStockLevel(item.product.id, currentStock + item.quantity);
    });

    this.cartItems.next([]);
    localStorage.removeItem('cart');
  }

  private saveCart(): void {
    localStorage.setItem('cart', JSON.stringify(this.cartItems.value));
  }

  private loadCart(): void {
    const savedCart = localStorage.getItem('cart');
    if (savedCart) {
      const items = JSON.parse(savedCart);
      this.cartItems.next(items);

      // Initialize stock levels for cart items
      items.forEach((item: CartItem) => {
        const initialStock = item.product.stock - item.quantity;
        this.updateStockLevel(item.product.id, initialStock);
      });
    }
  }
}
