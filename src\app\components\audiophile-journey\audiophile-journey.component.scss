.audiophile-journey {
  padding: 3rem 0;
  border-top: 1px solid #f0f0f0;
  border-bottom: 1px solid #f0f0f0;

  .section-title {
    position: relative;
    font-weight: 700;
    color: #333;
    font-size: 1.875rem;
    margin-bottom: 2rem;

    &::after {
      content: '';
      position: absolute;
      bottom: -10px;
      left: 50%;
      transform: translateX(-50%);
      width: 50px;
      height: 3px;
      background-color: var(--primary);
      border-radius: 3px;
    }
  }

  .journey-grid {
    display: grid;
    grid-template-columns: repeat(7, 1fr);
    gap: 1rem;
    margin: 0 auto;
    max-width: 1200px;

    @media (max-width: 1200px) {
      grid-template-columns: repeat(5, 1fr);
    }

    @media (max-width: 768px) {
      grid-template-columns: repeat(4, 1fr);
    }

    @media (max-width: 576px) {
      grid-template-columns: repeat(3, 1fr);
    }

    @media (max-width: 400px) {
      grid-template-columns: repeat(2, 1fr);
    }
  }

  .journey-item {
    text-align: center;
    transition: transform 0.3s ease;

    &:hover {
      transform: translateY(-5px);
    }
  }

  .journey-link {
    display: flex;
    flex-direction: column;
    align-items: center;
    text-decoration: none;
    color: #333;
  }

  .icon-container {
    width: 60px;
    height: 60px;
    margin-bottom: 0.5rem;
    display: flex;
    align-items: center;
    justify-content: center;
  }

  .category-icon {
    max-width: 100%;
    max-height: 100%;
    transition: transform 0.3s ease;
  }

  .category-name {
    font-size: 0.65rem;
    font-weight: 500;
    text-transform: uppercase;
    color: #666;
    line-height: 1.2;
    margin-top: 0.5rem;
  }

  .check-out-text {
    font-size: 0.75rem;
    font-weight: 600;
    color: var(--primary);
    letter-spacing: 1px;
    margin-top: 1rem;
    text-transform: uppercase;
    display: inline-block;
    padding: 0.5rem 1rem;
    border: 1px solid var(--primary);
  }
}