import {
  Component,
  OnInit,
  ViewChild,
  ElementRef,
  AfterViewInit,
  On<PERSON><PERSON>roy,
} from '@angular/core';
import { CommonModule } from '@angular/common';
import { RouterModule } from '@angular/router';

@Component({
  selector: 'app-video-banner',
  standalone: true,
  imports: [CommonModule, RouterModule],
  templateUrl: './video-banner.component.html',
  styleUrl: './video-banner.component.css',
})
export class VideoBannerComponent implements OnInit, AfterViewInit, OnDestroy {
  @ViewChild('videoElement') videoElementRef!: ElementRef<HTMLVideoElement>;
  @ViewChild('videoSection') videoSectionRef!: ElementRef<HTMLElement>;

  videoUrl =
    'https://www.headphonezone.in/cdn/shop/videos/c/vp/aa77c78ac6a34b0b9f42a760e1ea74b2/aa77c78ac6a34b0b9f42a760e1ea74b2.HD-720p-4.5Mbps-15289077.mp4?v=0';
  title = 'Headphone Zone';

  private videoElement: HTMLVideoElement | null = null;

  constructor() {}

  ngOnInit(): void {
    // Component initialization
  }

  ngAfterViewInit(): void {
    this.videoElement = this.videoElementRef.nativeElement;

    if (this.videoElement) {
      // Set video properties
      this.videoElement.muted = true;
      this.videoElement.loop = true;
      this.videoElement.playsInline = true;

      // Force autoplay
      this.playVideo();

      // Add event listener to handle any autoplay failures
      this.videoElement.addEventListener('pause', () => {
        this.playVideo();
      });
    }
  }

  ngOnDestroy(): void {
    if (this.videoElement) {
      this.videoElement.removeEventListener('pause', () => {
        this.playVideo();
      });
    }
  }

  private playVideo(): void {
    if (this.videoElement) {
      this.videoElement.play().catch((error) => {
        console.warn('Video autoplay failed:', error);
      });
    }
  }
}
