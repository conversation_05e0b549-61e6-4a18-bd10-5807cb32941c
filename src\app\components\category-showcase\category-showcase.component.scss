.category-card {
  position: relative;
  overflow: hidden;
  aspect-ratio: 4/5;
  border-radius: 0;

  .category-image {
    width: 100%;
    height: 100%;

    img {
      width: 100%;
      height: 100%;
      object-fit: cover;
    }
  }

  // Dark overlay gradient
  .category-image::after {
    content: '';
    position: absolute;
    inset: 0;
    background: linear-gradient(to top, rgba(0, 0, 0, 0.8) 0%, rgba(0, 0, 0, 0.4) 50%, rgba(0, 0, 0, 0.1) 100%);
    pointer-events: none;
  }

  // Text content
  h3 {
    text-shadow: 0 2px 4px rgba(0, 0, 0, 0.3);
  }

  p {
    text-shadow: 0 1px 2px rgba(0, 0, 0, 0.3);
  }

  // Button styling
  .inline-flex {
    opacity: 0.9;
    transform: translateX(0);
    transition: all 0.3s ease;
  }

  &:hover {
    .inline-flex {
      opacity: 1;
      transform: translateX(8px);
    }

    img {
      transform: scale(1.1);
    }
  }
}

// Section title styling
.section-title {
  font-size: 2rem;
  font-weight: 700;
  position: relative;
  padding-bottom: 1rem;

  &::after {
    content: '';
    position: absolute;
    bottom: 0;
    left: 50%;
    transform: translateX(-50%);
    width: 60px;
    height: 3px;
    background-color: var(--primary);
  }
}
