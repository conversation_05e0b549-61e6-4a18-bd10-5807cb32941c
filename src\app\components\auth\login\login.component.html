<div class="auth-container">
  <div class="auth-card">
    <div class="auth-header">
      <h1 class="auth-title">Login</h1>
      <p class="auth-subtitle">Welcome back to Headphone Zone</p>
    </div>

    <form [formGroup]="loginForm" (ngSubmit)="onSubmit()" class="auth-form">
      <mat-form-field appearance="outline" class="form-field">
        <mat-label>Email</mat-label>
        <input matInput type="email" formControlName="email" placeholder="<EMAIL>">
        <mat-error *ngIf="loginForm.get('email')?.invalid">{{ getErrorMessage('email') }}</mat-error>
      </mat-form-field>

      <mat-form-field appearance="outline" class="form-field">
        <mat-label>Password</mat-label>
        <input matInput [type]="hidePassword ? 'password' : 'text'" formControlName="password">
        <button mat-icon-button matSuffix type="button" (click)="hidePassword = !hidePassword">
          <mat-icon>{{ hidePassword ? 'visibility_off' : 'visibility' }}</mat-icon>
        </button>
        <mat-error *ngIf="loginForm.get('password')?.invalid">{{ getErrorMessage('password') }}</mat-error>
      </mat-form-field>

      <div class="form-actions">
        <mat-checkbox formControlName="rememberMe" color="primary">Remember me</mat-checkbox>
        <a routerLink="/account/reset-password" class="forgot-password">Forgot password?</a>
      </div>

      <button mat-raised-button color="primary" type="submit" class="submit-button" [disabled]="isLoading">
        <mat-spinner *ngIf="isLoading" diameter="20" class="spinner"></mat-spinner>
        <span *ngIf="!isLoading">Login</span>
      </button>

      <div class="social-login">
        <p class="divider"><span>or continue with</span></p>
        <div class="social-buttons">
          <button mat-stroked-button type="button" class="social-button">
            <img src="assets/images/icons/google.svg" alt="Google" class="social-icon">
            Google
          </button>
          <button mat-stroked-button type="button" class="social-button">
            <img src="assets/images/icons/facebook.svg" alt="Facebook" class="social-icon">
            Facebook
          </button>
        </div>
      </div>
    </form>

    <div class="auth-footer">
      <p>Don't have an account? <a routerLink="/account/register" class="register-link">Register</a></p>
    </div>
  </div>
</div>
