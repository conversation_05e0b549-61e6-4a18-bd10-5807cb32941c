.blog-detail-container {
  background-color: #f9fafb;
  min-height: calc(100vh - 200px);
}

.loading-container {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 4rem 0;
}

.loading-text {
  margin-top: 1rem;
  color: var(--text-secondary);
}

// Blog Post
.blog-post {
  max-width: 800px;
  margin: 0 auto;
  background-color: white;
  border-radius: 8px;
  box-shadow: 0 4px 6px rgba(0, 0, 0, 0.05), 0 1px 3px rgba(0, 0, 0, 0.1);
  overflow: hidden;
  margin-bottom: 3rem;
}

.post-header {
  padding: 2rem 2rem 1.5rem;
}

.post-meta {
  display: flex;
  align-items: center;
  margin-bottom: 1rem;
}

.post-category {
  background-color: var(--primary);
  color: white;
  padding: 0.25rem 0.75rem;
  border-radius: 20px;
  font-size: 0.75rem;
  font-weight: 500;
  margin-right: 1rem;
}

.post-date {
  color: var(--text-secondary);
  font-size: 0.875rem;
}

.post-title {
  font-size: 2.25rem;
  font-weight: 700;
  color: var(--text-primary);
  line-height: 1.3;
  margin-bottom: 1.5rem;
  
  @media (max-width: 640px) {
    font-size: 1.75rem;
  }
}

.post-author {
  display: flex;
  align-items: center;
}

.author-avatar {
  width: 48px;
  height: 48px;
  border-radius: 50%;
  margin-right: 1rem;
}

.author-name {
  display: block;
  font-weight: 600;
  color: var(--text-primary);
  margin-bottom: 0.25rem;
}

.author-title {
  font-size: 0.875rem;
  color: var(--text-secondary);
}

.featured-image {
  width: 100%;
  height: 400px;
  
  @media (max-width: 640px) {
    height: 250px;
  }
}

.post-content {
  padding: 2rem;
  color: var(--text-primary);
  line-height: 1.8;
  font-size: 1.125rem;
  
  h2 {
    font-size: 1.75rem;
    font-weight: 600;
    margin: 2rem 0 1rem;
  }
  
  h3 {
    font-size: 1.5rem;
    font-weight: 600;
    margin: 1.5rem 0 1rem;
  }
  
  p {
    margin-bottom: 1.5rem;
  }
  
  ul, ol {
    margin-bottom: 1.5rem;
    padding-left: 1.5rem;
  }
  
  li {
    margin-bottom: 0.5rem;
  }
  
  img {
    max-width: 100%;
    height: auto;
    border-radius: 8px;
    margin: 1.5rem 0;
  }
  
  blockquote {
    border-left: 4px solid var(--primary);
    padding-left: 1.5rem;
    font-style: italic;
    margin: 1.5rem 0;
    color: var(--text-secondary);
  }
  
  a {
    color: var(--primary);
    text-decoration: none;
    
    &:hover {
      text-decoration: underline;
    }
  }
}

.post-tags {
  padding: 0 2rem 1.5rem;
  display: flex;
  align-items: center;
  flex-wrap: wrap;
  
  @media (max-width: 640px) {
    flex-direction: column;
    align-items: flex-start;
  }
}

.tag-label {
  font-weight: 600;
  color: var(--text-primary);
  margin-right: 0.75rem;
  
  @media (max-width: 640px) {
    margin-bottom: 0.5rem;
  }
}

.tags-list {
  display: flex;
  flex-wrap: wrap;
  gap: 0.5rem;
}

.tag {
  padding: 0.25rem 0.75rem;
  border-radius: 20px;
  background-color: #f3f4f6;
  color: var(--text-secondary);
  font-size: 0.875rem;
  text-decoration: none;
  transition: all 0.2s ease;
  
  &:hover {
    background-color: #e5e7eb;
    color: var(--text-primary);
  }
}

.share-container {
  padding: 1.5rem 2rem 2rem;
  border-top: 1px solid #e5e7eb;
  display: flex;
  align-items: center;
  
  @media (max-width: 640px) {
    flex-direction: column;
    align-items: flex-start;
  }
}

.share-label {
  font-weight: 600;
  color: var(--text-primary);
  margin-right: 1rem;
  
  @media (max-width: 640px) {
    margin-bottom: 0.75rem;
  }
}

.share-buttons {
  display: flex;
  gap: 0.5rem;
}

// Related Posts
.related-posts {
  margin-bottom: 4rem;
}

.section-title {
  font-size: 1.5rem;
  font-weight: 600;
  color: var(--text-primary);
  margin-bottom: 1.5rem;
  text-align: center;
}

.related-post-card {
  height: 350px;
}

.post-card {
  display: block;
  height: 100%;
  border-radius: 8px;
  overflow: hidden;
  box-shadow: 0 4px 6px rgba(0, 0, 0, 0.05), 0 1px 3px rgba(0, 0, 0, 0.1);
  background-color: white;
  transition: transform 0.3s ease, box-shadow 0.3s ease;
  
  &:hover {
    transform: translateY(-5px);
    box-shadow: 0 10px 15px rgba(0, 0, 0, 0.1);
  }
}

.post-image {
  height: 180px;
  position: relative;
  overflow: hidden;
}

.post-content {
  padding: 1.5rem;
}

.post-title {
  font-size: 1.125rem;
  font-weight: 600;
  color: var(--text-primary);
  margin-bottom: 0.75rem;
  line-height: 1.4;
}

.post-excerpt {
  color: var(--text-secondary);
  font-size: 0.875rem;
  margin-bottom: 1rem;
  line-height: 1.6;
  display: -webkit-box;
  -webkit-line-clamp: 3;
  -webkit-box-orient: vertical;
  overflow: hidden;
}

// Newsletter
.blog-newsletter {
  margin-top: 4rem;
  margin-bottom: 2rem;
}

.newsletter-container {
  background-color: white;
  border-radius: 8px;
  box-shadow: 0 4px 6px rgba(0, 0, 0, 0.05), 0 1px 3px rgba(0, 0, 0, 0.1);
  padding: 3rem 2rem;
  text-align: center;
}

.newsletter-title {
  font-size: 1.75rem;
  font-weight: 600;
  color: var(--text-primary);
  margin-bottom: 1rem;
}

.newsletter-description {
  color: var(--text-secondary);
  max-width: 600px;
  margin: 0 auto 2rem;
}

.newsletter-form {
  display: flex;
  max-width: 500px;
  margin: 0 auto;
  
  @media (max-width: 640px) {
    flex-direction: column;
  }
}

.email-field {
  flex: 1;
  margin-right: 1rem;
  
  @media (max-width: 640px) {
    margin-right: 0;
    margin-bottom: 1rem;
  }
}

.subscribe-button {
  height: 56px;
  min-width: 120px;
  
  @media (max-width: 640px) {
    width: 100%;
  }
}
