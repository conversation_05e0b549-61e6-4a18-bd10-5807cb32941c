:host {
  display: block;
}

// Form field styling
::ng-deep {
  .mat-mdc-form-field {
    .mat-mdc-form-field-flex {
      background-color: #ffffff;
    }
    
    .mat-mdc-text-field-wrapper {
      border-radius: 4px;
      border: 1px solid var(--border-color);
      padding-left: 0.5rem;
      padding-right: 0.5rem;
      background-color: #ffffff;
      
      &:hover {
        border-color: #999999;
      }
    }
    
    .mat-mdc-form-field-label {
      color: var(--text-secondary);
      font-size: 0.9rem;
    }
    
    &.mat-focused {
      .mat-mdc-text-field-wrapper {
        border-color: var(--primary);
      }
    }
  }
  
  // Remove underline
  .mdc-line-ripple {
    display: none;
  }
}

// Social icons
.social-icon {
  transition: all 0.3s ease;
  
  &:hover {
    transform: translateY(-2px);
  }
}

// Footer links
a {
  position: relative;
  display: inline-block;
  
  &:hover {
    padding-left: 2px;
  }
}

// Payment methods
.payment-methods {
  img {
    filter: grayscale(100%);
    opacity: 0.7;
    transition: all 0.3s ease;
    
    &:hover {
      filter: grayscale(0%);
      opacity: 1;
    }
  }
}

// Subscribe button
button[mat-raised-button] {
  background-color: var(--primary);
  color: white;
  font-weight: 600;
  letter-spacing: 0.5px;
  
  &:hover {
    background-color: darken(#ff5500, 5%);
  }
}
