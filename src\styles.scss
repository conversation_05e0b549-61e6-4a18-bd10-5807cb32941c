/* You can add global styles to this file, and also import other style files */
@tailwind base;
@tailwind components;
@tailwind utilities;

/* Angular Material Theme */
@import '@angular/material/prebuilt-themes/indigo-pink.css';

/* Import Google Fonts */
@import url('https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&display=swap');

/* CSS Variables - Headphone Zone Theme */
:root {
  --primary: #ff5500;
  --primary-rgb: 255, 85, 0;
  --secondary: #333333;
  --secondary-rgb: 51, 51, 51;
  --accent: #ff9800;
  --accent-rgb: 255, 152, 0;
  --success: #4caf50;
  --success-rgb: 76, 175, 80;
  --warning: #ffeb3b;
  --warning-rgb: 255, 235, 59;
  --error: #f44336;
  --error-rgb: 244, 67, 54;
  --text-primary: #333333;
  --text-secondary: #666666;
  --text-light: #999999;
  --border-color: #e5e5e5;
  --background-light: #f9f9f9;
}

/* Global Styles - Headphone Zone */
html, body {
  height: 100%;
  margin: 0;
  font-family: 'Inter', 'Roboto', sans-serif;
  scroll-behavior: smooth;
  background-color: #ffffff;
  color: var(--text-primary);
  line-height: 1.5;
  font-size: 16px;
}

/* Custom utility classes */
.container {
  @apply mx-auto px-4 max-w-7xl;
}

.btn {
  @apply px-4 py-2 rounded transition-all duration-300 font-medium;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.05);

  &:hover {
    box-shadow: 0 2px 5px rgba(0, 0, 0, 0.1);
  }

  &:active {
    transform: translateY(1px);
  }
}

.btn-primary {
  @apply bg-primary text-white hover:bg-opacity-90;
  text-transform: uppercase;
  letter-spacing: 0.5px;
  font-weight: 600;

  &:hover {
    background-color: darken(#ff5500, 5%);
  }
}

.btn-secondary {
  @apply bg-secondary text-white hover:bg-opacity-90;

  &:hover {
    background-color: #444444;
  }
}

.btn-outline {
  @apply border border-primary text-primary bg-transparent;

  &:hover {
    @apply bg-primary text-white;
  }
}

.card {
  @apply bg-white rounded p-4 transition-all duration-300;
  border: 1px solid var(--border-color);

  &:hover {
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.05);
  }
}

.section-title {
  @apply text-2xl md:text-3xl font-semibold mb-6 text-center;
  position: relative;
  display: inline-block;
  padding-bottom: 10px;
  color: var(--text-primary);

  &::after {
    content: '';
    position: absolute;
    bottom: 0;
    left: 50%;
    transform: translateX(-50%);
    width: 40px;
    height: 2px;
    background-color: var(--primary);
  }
}

.section-header {
  font-size: 1.25rem;
  font-weight: 600;
  color: var(--text-primary);
  margin-bottom: 1.25rem;
  position: relative;
  padding-bottom: 0.75rem;

  &:after {
    content: '';
    position: absolute;
    bottom: 0;
    left: 0;
    width: 40px;
    height: 2px;
    background-color: var(--primary);
  }
}

/* Material overrides */
.mat-mdc-icon-button {
  --mdc-icon-button-state-layer-size: 40px;
}

.mat-badge-content {
  font-weight: 600 !important;
}

/* Headphone Zone specific styles */
.product-card {
  @apply bg-white rounded overflow-hidden transition-all duration-300;
  border: 1px solid var(--border-color);

  &:hover {
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.08);

    .product-image img {
      transform: scale(1.05);
    }
  }

  .product-image {
    @apply overflow-hidden relative;

    img {
      @apply w-full h-auto object-contain transition-transform duration-300;
      aspect-ratio: 1/1;
      background-color: #f9f9f9;
    }

    .product-badge {
      @apply absolute top-2 left-2 px-2 py-1 text-xs font-medium text-white rounded;
      background-color: var(--primary);
    }
  }

  .product-info {
    @apply p-4;

    .product-title {
      @apply text-sm font-medium mb-1 line-clamp-2;
      color: var(--text-primary);
      min-height: 2.5rem;
    }

    .product-price {
      @apply flex items-center gap-2;

      .current-price {
        @apply font-semibold;
        color: var(--text-primary);
      }

      .original-price {
        @apply text-sm line-through;
        color: var(--text-light);
      }

      .discount {
        @apply text-xs font-medium;
        color: #4caf50;
      }
    }
  }
}

.breadcrumb {
  @apply flex items-center text-sm mb-6;

  a {
    @apply text-gray-500 hover:text-primary transition-colors;
    text-decoration: none;
  }

  .separator {
    @apply mx-2 text-gray-400;
  }

  .current {
    @apply font-medium;
    color: var(--primary);
  }
}

.tag {
  @apply inline-flex items-center px-2 py-1 rounded-full text-xs font-medium mr-2 mb-2;
  background-color: var(--background-light);
  color: var(--text-secondary);

  &.tag-primary {
    background-color: rgba(var(--primary-rgb), 0.1);
    color: var(--primary);
  }
}

/* Scrollbar styling */
::-webkit-scrollbar {
  width: 6px;
  height: 6px;
}

::-webkit-scrollbar-track {
  background: #f1f1f1;
}

::-webkit-scrollbar-thumb {
  background: #c1c1c1;
  border-radius: 3px;
}

::-webkit-scrollbar-thumb:hover {
  background: #a8a8a8;
}
