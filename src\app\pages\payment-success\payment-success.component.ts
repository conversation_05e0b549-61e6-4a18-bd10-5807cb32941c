import { Component, OnInit } from '@angular/core';
import { CommonModule } from '@angular/common';
import { RouterModule } from '@angular/router';
import { MaterialModule } from '../../material/material.module';
import { CartService } from '../../services/cart.service';
import {
  trigger,
  transition,
  style,
  animate,
  query,
  stagger,
} from '@angular/animations';

interface NextStep {
  icon: string;
  title: string;
  description: string;
  action: string;
  link: string;
}

@Component({
  selector: 'app-payment-success',
  standalone: true,
  imports: [CommonModule, RouterModule, MaterialModule],
  templateUrl: './payment-success.component.html',
  styleUrl: './payment-success.component.scss',
  animations: [
    trigger('fadeIn', [
      transition(':enter', [
        style({ opacity: 0, transform: 'translateY(20px)' }),
        animate('0.5s ease', style({ opacity: 1, transform: 'translateY(0)' })),
      ]),
    ]),
    trigger('listAnimation', [
      transition('* => *', [
        query(
          ':enter',
          [
            style({ opacity: 0, transform: 'translateY(20px)' }),
            stagger(100, [
              animate(
                '0.5s ease',
                style({ opacity: 1, transform: 'translateY(0)' })
              ),
            ]),
          ],
          { optional: true }
        ),
      ]),
    ]),
  ],
})
export class PaymentSuccessComponent implements OnInit {
  orderNumber = this.generateOrderNumber();
  estimatedDelivery = this.calculateEstimatedDelivery();
  orderAmount = 0;

  nextSteps: NextStep[] = [
    {
      icon: 'inventory_2',
      title: 'Track Your Order',
      description: "Follow your package's journey to your doorstep",
      action: 'Track Order',
      link: '/track-order',
    },
    {
      icon: 'shopping_bag',
      title: 'Continue Shopping',
      description: 'Explore more products in our catalog',
      action: 'Shop Now',
      link: '/shop',
    },
    {
      icon: 'account_circle',
      title: 'View Account',
      description: 'Check your order history and preferences',
      action: 'Go to Account',
      link: '/account',
    },
  ];

  constructor(private cartService: CartService) {}

  ngOnInit(): void {
    // Get the order amount from the cart service
    this.cartService.getCartTotal().subscribe((total) => {
      this.orderAmount = total;
      // Clear the cart after successful payment
      this.cartService.clearCart();
    });
  }

  private generateOrderNumber(): string {
    const timestamp = new Date().getTime().toString().slice(-8);
    const random = Math.floor(Math.random() * 10000)
      .toString()
      .padStart(4, '0');
    return `ORD-${timestamp}-${random}`;
  }

  private calculateEstimatedDelivery(): Date {
    const delivery = new Date();
    delivery.setDate(delivery.getDate() + 5); // Estimate 5 days for delivery
    return delivery;
  }

  formatDate(date: Date): string {
    return date.toLocaleDateString('en-US', {
      weekday: 'long',
      month: 'long',
      day: 'numeric',
      year: 'numeric',
    });
  }

  copyOrderNumber(): void {
    navigator.clipboard.writeText(this.orderNumber).then(() => {
      // Could show a toast notification here
    });
  }
}
