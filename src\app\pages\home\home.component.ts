import { Component } from '@angular/core';
import { CommonModule } from '@angular/common';
import { HeaderComponent } from '../../components/header/header.component';
import { BannerSliderComponent } from '../../components/banner-slider/banner-slider.component';
import { FeaturedDealsComponent } from '../../components/featured-deals/featured-deals.component';
import { TrustIndicatorsComponent } from '../../components/trust-indicators/trust-indicators.component';
import { ProductGridComponent } from '../../components/product-grid/product-grid.component';
import { PurchaseToastComponent } from '../../components/purchase-toast/purchase-toast.component';
import { CategoryShowcaseComponent } from '../../components/category-showcase/category-showcase.component';
import { BrandShowcaseComponent } from '../../components/brand-showcase/brand-showcase.component';
import { NewsletterComponent } from '../../components/newsletter/newsletter.component';
import { FooterComponent } from '../../components/footer/footer.component';
import { ProductComparisonComponent } from '../../components/product-comparison/product-comparison.component';
import { SoundSignatureComponent } from '../../components/sound-signature/sound-signature.component';
import { RediscoverMusicComponent } from '../../components/rediscover-music/rediscover-music.component';
import { CompanyHistoryComponent } from '../../components/company-history/company-history.component';
import { FaqSectionComponent } from '../../components/faq-section/faq-section.component';
import { HeadphoneConnectComponent } from '../../components/headphone-connect/headphone-connect.component';
import { VideoBannerComponent } from '../../components/video-banner/video-banner.component';
import { AudiophileJourneyComponent } from '../../components/audiophile-journey/audiophile-journey.component';
import { SoundSignatureIndicatorComponent } from '../../components/sound-signature-indicator/sound-signature-indicator.component';

@Component({
  selector: 'app-home',
  standalone: true,
  imports: [
    CommonModule,
    HeaderComponent,
    BannerSliderComponent,
    VideoBannerComponent,
    AudiophileJourneyComponent,
    FeaturedDealsComponent,
    TrustIndicatorsComponent,
    CategoryShowcaseComponent,
    ProductGridComponent,
    BrandShowcaseComponent,
    NewsletterComponent,
    FooterComponent,
    PurchaseToastComponent,
    ProductComparisonComponent,
    SoundSignatureComponent,
    RediscoverMusicComponent,
    CompanyHistoryComponent,
    FaqSectionComponent,
    HeadphoneConnectComponent,
    SoundSignatureIndicatorComponent,
  ],
  template: `
    <app-header></app-header>
    <main>
      <app-banner-slider></app-banner-slider>
      <app-video-banner></app-video-banner>
      <app-audiophile-journey></app-audiophile-journey>

      <!-- Music Lovers Community Counter Section -->
      <section
        class="music-lovers-section py-16 bg-gradient-to-r from-primary/5 to-primary/10"
      >
        <div class="container mx-auto px-4">
          <app-sound-signature-indicator></app-sound-signature-indicator>
        </div>
      </section>

      <app-category-showcase></app-category-showcase>
      <app-featured-deals></app-featured-deals>
      <app-product-grid></app-product-grid>
      <app-rediscover-music></app-rediscover-music>
      <app-sound-signature></app-sound-signature>
      <app-product-comparison></app-product-comparison>
      <app-brand-showcase></app-brand-showcase>
      <app-headphone-connect></app-headphone-connect>
      <app-company-history></app-company-history>
      <app-faq-section></app-faq-section>
      <app-trust-indicators></app-trust-indicators>
      <app-newsletter></app-newsletter>
    </main>
    <app-footer></app-footer>
    <app-purchase-toast></app-purchase-toast>
  `,
  styles: [
    `
      main {
        min-height: calc(100vh - 70px);
      }
      .music-lovers-section {
        position: relative;
        overflow: hidden;
      }
    `,
  ],
})
export class HomeComponent {}
