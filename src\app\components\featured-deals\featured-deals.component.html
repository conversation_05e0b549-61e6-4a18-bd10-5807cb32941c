<section class="featured-deals py-12" aria-labelledby="featured-deals-title">
  <div class="container mx-auto px-4">
    <div class="text-center mb-8">
      <h2 id="featured-deals-title" class="section-title inline-block">Our Collaborations</h2>
      <p class="text-gray-600 mt-3 max-w-2xl mx-auto">Discover our exclusive collaborations with the world's best audio brands, designed to deliver exceptional sound experiences at unbeatable value.</p>
    </div>

    <ng-container *ngIf="featuredProducts$ | async as featuredProducts">
      <div class="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-4 gap-6 mt-8" [@dealAnimation]="featuredProducts.length">
        <div *ngFor="let product of featuredProducts"
          class="product-card relative overflow-hidden group"
          [attr.aria-label]="'Product: ' + product.name">

          <!-- Discount Badge -->
          <div *ngIf="product.discountedPrice" class="absolute top-2 left-2 z-10">
            <div class="bg-primary text-white text-xs font-medium px-2 py-1 rounded product-badge" role="status">
              {{ getDiscountPercentage(product) }}% OFF
            </div>
          </div>

          <!-- New Badge -->
          <div *ngIf="product.isHot" class="absolute top-2 right-2 z-10">
            <div class="bg-secondary text-white text-xs font-medium px-2 py-1 rounded product-badge" role="status">
              NEW
            </div>
          </div>

          <!-- Product Image -->
          <div class="product-image">
            <a [routerLink]="['/product', product.id]">
              <img [src]="product.imageUrl" [alt]="product.name"
                class="w-full h-auto transition-transform duration-500">
            </a>

            <!-- Quick Actions Overlay -->
            <div class="absolute inset-0 bg-black bg-opacity-0 group-hover:bg-opacity-10 transition-all duration-300 flex items-center justify-center">
              <div class="quick-actions opacity-0 group-hover:opacity-100 transition-opacity duration-300">
                <button mat-mini-fab color="primary" aria-label="Quick view" class="mx-1" [routerLink]="['/product', product.id]">
                  <mat-icon>visibility</mat-icon>
                </button>
                <button mat-mini-fab color="primary" aria-label="Add to wishlist" class="mx-1">
                  <mat-icon>favorite_border</mat-icon>
                </button>
              </div>
            </div>
          </div>

          <!-- Product Info -->
          <div class="product-info">
            <h3 class="product-title">
              <a [routerLink]="['/product', product.id]">{{ product.name }}</a>
            </h3>

            <!-- Rating -->
            <div class="flex items-center mb-2">
              <div class="flex">
                <mat-icon class="text-amber-400 text-sm">star</mat-icon>
                <mat-icon class="text-amber-400 text-sm">star</mat-icon>
                <mat-icon class="text-amber-400 text-sm">star</mat-icon>
                <mat-icon class="text-amber-400 text-sm">star</mat-icon>
                <mat-icon class="text-amber-400 text-sm">star_half</mat-icon>
              </div>
              <span class="text-xs text-gray-500 ml-1">({{ 10 + product.id * 5 }})</span>
            </div>

            <!-- Price -->
            <div class="product-price">
              <span *ngIf="product.discountedPrice" class="original-price" aria-label="Original price">
                ₹{{ product.price.toFixed(2) }}
              </span>
              <span class="current-price" aria-label="Current price">
                ₹{{ (product.discountedPrice || product.price).toFixed(2) }}
              </span>
              <span *ngIf="product.discountedPrice" class="discount">
                Save ₹{{ (product.price - product.discountedPrice).toFixed(2) }}
              </span>
            </div>

            <!-- Stock Status -->
            <ng-container *ngIf="getRemainingStock(product.id) | async as stock">
              <div class="mt-2 text-xs" [ngSwitch]="getStockWarningLevel(stock)">
                <span *ngSwitchCase="'low'" class="text-error font-medium">Only {{ stock }} left!</span>
                <span *ngSwitchCase="'medium'" class="text-warning font-medium">Selling fast!</span>
                <span *ngSwitchCase="'high'" class="text-success font-medium">In Stock</span>
              </div>
            </ng-container>

            <!-- Add to Cart Button -->
            <button (click)="addToCart(product)" class="w-full mt-3 py-2 bg-orange-600 text-white rounded-none hover:bg-opacity-90 transition-all text-sm uppercase font-medium"
              [disabled]="(getRemainingStock(product.id) | async) === 0"
              [attr.aria-label]="'Add ' + product.name + ' to cart'">
              {{ (getRemainingStock(product.id) | async) === 0 ? 'Out of Stock' : 'Add to Cart' }}
            </button>
          </div>
        </div>
      </div>
    </ng-container>

    <!-- View All Button -->
    <div class="text-center mt-10">
      <a routerLink="/collections/our-collabs" class="btn-outline inline-flex items-center px-6 py-2 border border-gray-800 text-gray-800 hover:bg-gray-800 hover:text-white transition-colors">
        <span>View All Collaborations</span>
        <span class="ml-2">→</span>
      </a>
    </div>
  </div>
</section>