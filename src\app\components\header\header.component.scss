:host {
  display: block;
  position: relative;
}

// Top bar styles
.bg-secondary {
  background-color: var(--secondary);
}

// Header styles
header {
  transition: all 0.3s ease;

  &.shadow-sm {
    box-shadow: 0 2px 5px rgba(0, 0, 0, 0.05);
  }

  .container {
    height: 100%;
  }

  a {
    text-decoration: none;

    &:hover {
      opacity: 0.9;
    }
  }

  .text-primary {
    color: var(--primary);
  }
}

// Navigation and mega menu styles
.nav-item {
  position: relative;

  .nav-button {
    position: relative;
    padding: 0.5rem 0;
    color: var(--text-primary);
    font-size: 0.95rem;
    transition: all 0.2s ease;

    &:hover, &.active {
      color: var(--primary);
    }

    &.active::after {
      content: '';
      position: absolute;
      bottom: 0;
      left: 0;
      width: 100%;
      height: 2px;
      background-color: var(--primary);
      transform: scaleX(1);
      transform-origin: center;
      transition: transform 0.3s ease;
    }

    &:not(.active)::after {
      content: '';
      position: absolute;
      bottom: 0;
      left: 0;
      width: 100%;
      height: 2px;
      background-color: var(--primary);
      transform: scaleX(0);
      transform-origin: center;
      transition: transform 0.3s ease;
    }

    &:hover::after {
      transform: scaleX(1);
    }
  }

  // Mega menu styling
  .mega-menu {
    box-shadow: 0 10px 25px rgba(0, 0, 0, 0.1);
    border-radius: 0 0 8px 8px;
    border-top: 3px solid var(--primary);
    z-index: 100;

    h2 {
      font-size: 1.25rem;
      font-weight: 700;
      color: var(--text-primary);
    }

    h3 {
      font-size: 0.95rem;
      font-weight: 600;
      border-bottom-color: var(--border-color);
      padding-bottom: 0.5rem;
      margin-bottom: 1rem;
      color: var(--text-primary);
    }

    .menu-link {
      position: relative;
      padding: 0.5rem 0;
      font-size: 0.85rem;
      color: var(--text-secondary);
      transition: all 0.2s ease;
      display: flex;
      align-items: center;

      &:hover {
        color: var(--primary);
        transform: translateX(3px);
      }

      &::before {
        content: '';
        position: absolute;
        left: -10px;
        width: 0;
        height: 0;
        border-style: solid;
        border-width: 4px 0 4px 6px;
        border-color: transparent transparent transparent var(--primary);
        opacity: 0;
        transition: all 0.2s ease;
      }

      &:hover::before {
        opacity: 1;
        left: -15px;
      }

      img {
        transition: all 0.3s ease;
      }

      &:hover img {
        transform: scale(1.1);
      }
    }
  }
}

// Search container styling - Headphone Zone style
.search-container {
  background-color: #ffffff;
  border-radius: 4px;
  height: 36px;
  transition: all 0.3s ease;

  &:hover {
    border-color: #999;
  }

  input {
    font-size: 14px;

    &::placeholder {
      color: #999;
    }

    &:focus {
      outline: none;
    }
  }
}

.mat-mdc-form-field-infix {
  width: auto;
}

// Mobile menu styles
.mat-expansion-panel {
  box-shadow: none !important;
  background-color: transparent;

  .mat-expansion-panel-header {
    padding: 0 16px;
    height: 48px;

    &:hover {
      background-color: rgba(0, 0, 0, 0.02);
    }
  }

  .mat-expansion-panel-body {
    padding: 0 16px 16px;
  }
}

// Nested accordion in mobile menu
.nested-accordion {
  margin-left: -16px;
  margin-right: -16px;

  .mat-expansion-panel {
    border-bottom: 1px solid rgba(0, 0, 0, 0.05) !important;

    &:last-child {
      border-bottom: none !important;
    }

    .mat-expansion-panel-header {
      padding-left: 32px;
      font-size: 14px;
      height: 42px;
    }

    .mat-expansion-panel-body {
      padding-left: 32px;
    }
  }
}

.mat-nav-list {
  padding-top: 0;
  padding-bottom: 0;

  .mat-list-item {
    height: 36px;
    font-size: 14px;

    &:hover {
      background-color: rgba(0, 0, 0, 0.02);
    }
  }
}

::ng-deep {
  .mat-mdc-menu-panel {
    border-radius: 8px;
    overflow: hidden;
    box-shadow: 0 4px 20px rgba(0, 0, 0, 0.15);

    .mat-mdc-menu-item {
      font-size: 14px;

      .mat-icon {
        margin-right: 8px;
      }
    }
  }

  .mat-badge-content {
    font-size: 10px;
    width: 18px;
    height: 18px;
    line-height: 18px;
    font-weight: 600;
    transition: transform 0.3s cubic-bezier(0.4, 0, 0.2, 1);

    &.mat-badge-active {
      animation: badgePop 0.3s cubic-bezier(0.4, 0, 0.2, 1);
    }
  }

  .mat-mdc-form-field-subscript-wrapper {
    height: 0;
    transform: translateY(-4px);
  }
}

@keyframes badgePop {
  0% {
    transform: scale(0.8);
  }
  50% {
    transform: scale(1.2);
  }
  100% {
    transform: scale(1);
  }
}

// Animation for search expansion
.search-container {
  transition: all 0.3s ease;
}

// Search transitions
.search-bar {
  transition: all 0.3s ease;

  &.collapsed {
    width: 0;
    opacity: 0;
    pointer-events: none;
  }

  &.expanded {
    width: 100%;
    opacity: 1;
  }
}

// Custom styling for the cart count
.cart-count {
  font-size: 10px;
  width: 18px;
  height: 18px;
  line-height: 18px;
  font-weight: 600;
  background-color: #2563eb;
}

// Navigation styling
.nav-item {
  position: relative;
}

// Show dropdown when active
.nav-item.active-dropdown .category-dropdown,
.nav-item.active-dropdown .mega-menu {
  display: block;
}

.category-dropdown,
.mega-menu {
  display: none;
  animation: fadeIn 0.2s ease-out;

  h3 {
    color: #333;
    font-size: 14px;
    font-weight: 600;
    text-transform: uppercase;
  }

  ul li a {
    color: #666;
    font-size: 13px;
    line-height: 1.8;

    &:hover {
      color: #2563eb;
    }
  }
}

.dropdown-arrow-container {
  cursor: pointer;
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 4px;

  &:hover .dropdown-arrow {
    color: var(--primary);
    border-color: var(--primary);
    background-color: rgba(var(--primary-rgb), 0.05);
  }
}

.dropdown-arrow {
  cursor: pointer;
  transition: transform 0.2s ease;
  color: #666;
  border: 1px solid #ddd;
  border-radius: 3px;
  padding: 2px;
  font-size: 16px;
  width: 20px;
  height: 20px;
  display: flex;
  align-items: center;
  justify-content: center;

  &:hover {
    color: var(--primary);
    border-color: var(--primary);
    background-color: rgba(var(--primary-rgb), 0.05);
  }

  .active-dropdown & {
    color: var(--primary);
    border-color: var(--primary);
    background-color: rgba(var(--primary-rgb), 0.1);
  }
}

// Icon buttons
button[mat-icon-button] {
  transition: transform 0.2s ease;

  &:hover {
    transform: scale(1.1);
    color: var(--primary);
  }
}

// User menu
.mat-mdc-menu-panel {
  border-radius: 8px;
  overflow: hidden;
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.15);

  .mat-mdc-menu-item {
    font-size: 14px;

    .mat-icon {
      margin-right: 8px;
    }
  }
}

// Cart drawer
.fixed.inset-0 {
  z-index: 1000;

  .fixed.top-0.right-0.bottom-0 {
    max-width: 400px;
    transition: transform 0.3s cubic-bezier(0.4, 0, 0.2, 1);

    h2 {
      font-size: 1.25rem;
      font-weight: 600;
      color: var(--text-primary);
    }

    .overflow-y-auto {
      &::-webkit-scrollbar {
        width: 4px;
      }

      &::-webkit-scrollbar-track {
        background: #f1f1f1;
      }

      &::-webkit-scrollbar-thumb {
        background: #c1c1c1;
        border-radius: 2px;
      }
    }
  }
}

// Mobile menu drawer
.fixed.top-0.left-0.bottom-0 {
  max-width: 320px;
  transition: transform 0.3s cubic-bezier(0.4, 0, 0.2, 1);

  h2 {
    font-size: 1.25rem;
    font-weight: 600;
    color: var(--text-primary);
  }
}

// Cart total
.cart-total {
  transition: all 0.3s ease;

  &:hover {
    transform: scale(1.05);
  }
}

// Loading spinner styles
mat-progress-spinner {
  display: inline-block;
  vertical-align: middle;

  ::ng-deep circle {
    stroke: currentColor;
  }
}

// Error state
.text-error {
  color: #dc2626;
}

// Enhance focus styles
button:focus-visible,
a:focus-visible {
  outline: none;
  box-shadow: 0 0 0 2px var(--primary), 0 0 0 4px white;
}

// Screen reader only text
.sr-only {
  position: absolute;
  width: 1px;
  height: 1px;
  padding: 0;
  margin: -1px;
  overflow: hidden;
  white-space: nowrap;
  border: 0;
  clip: rect(0, 0, 0, 0);
  clip-path: inset(50%);
}

// Responsive adjustments
@media (max-width: 768px) {
  .search-expanded {
    position: absolute;
    top: 100%;
    left: 0;
    right: 0;
    padding: 1rem;
    background: white;
    box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
    animation: slideDown 0.3s ease;
  }

  @keyframes slideDown {
    from {
      opacity: 0;
      transform: translateY(-10px);
    }
    to {
      opacity: 1;
      transform: translateY(0);
    }
  }
}

.mat-toolbar {
  transition: box-shadow 0.3s ease;

  &.scrolled {
    box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06);
  }
}

// Reduced motion
@media (prefers-reduced-motion: reduce) {
  header,
  .mat-badge-content,
  .cart-drawer,
  .cart-total,
  nav a::after {
    transition: none !important;
    animation: none !important;
  }
}

// Print styles
@media print {
  header {
    position: static;
    box-shadow: none;
    border-bottom: 1px solid #ddd;
  }

  .cart-drawer,
  .mobile-menu-button,
  .search-toggle {
    display: none !important;
  }
}