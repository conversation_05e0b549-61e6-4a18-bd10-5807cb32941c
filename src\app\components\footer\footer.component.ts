import { Component } from '@angular/core';
import { CommonModule } from '@angular/common';
import { RouterModule } from '@angular/router';
import { MaterialModule } from '../../material/material.module';
import { FormsModule, ReactiveFormsModule } from '@angular/forms';

@Component({
  selector: 'app-footer',
  standalone: true,
  imports: [
    CommonModule,
    RouterModule,
    MaterialModule,
    FormsModule,
    ReactiveFormsModule
  ],
  templateUrl: './footer.component.html',
  styleUrls: ['./footer.component.scss']
})
export class FooterComponent {
  currentYear = new Date().getFullYear();
  
  footerLinks = [
    {
      title: 'Shop',
      links: [
        { name: 'In-Ears', url: '/collections/in-ear-monitors' },
        { name: 'Headphones', url: '/collections/headphones' },
        { name: 'Wireless', url: '/collections/wireless' },
        { name: 'DACs & Amps', url: '/collections/dacs-amps' },
        { name: 'Accessories', url: '/collections/accessories' }
      ]
    },
    {
      title: 'Information',
      links: [
        { name: 'About Us', url: '/pages/about-us' },
        { name: 'Headphone Connect', url: '/pages/headphone-connect' },
        { name: 'Careers', url: '/pages/careers' },
        { name: 'Blog', url: '/blogs/audiophile-101' },
        { name: 'Press', url: '/pages/press' }
      ]
    },
    {
      title: 'Help',
      links: [
        { name: 'FAQs', url: '/pages/faqs' },
        { name: 'Shipping & Returns', url: '/pages/shipping-returns' },
        { name: 'Warranty', url: '/pages/warranty' },
        { name: 'Track Order', url: '/pages/track-order' },
        { name: 'Contact Us', url: '/pages/contact-us' }
      ]
    },
    {
      title: 'Policies',
      links: [
        { name: 'Terms of Service', url: '/pages/terms-of-service' },
        { name: 'Privacy Policy', url: '/pages/privacy-policy' },
        { name: 'Refund Policy', url: '/pages/refund-policy' },
        { name: 'Shipping Policy', url: '/pages/shipping-policy' },
        { name: 'Cancellation Policy', url: '/pages/cancellation-policy' }
      ]
    }
  ];
  
  paymentMethods = [
    { name: 'Visa', icon: 'assets/images/payment/visa.svg' },
    { name: 'Mastercard', icon: 'assets/images/payment/mastercard.svg' },
    { name: 'American Express', icon: 'assets/images/payment/amex.svg' },
    { name: 'RuPay', icon: 'assets/images/payment/rupay.svg' },
    { name: 'UPI', icon: 'assets/images/payment/upi.svg' },
    { name: 'PayPal', icon: 'assets/images/payment/paypal.svg' },
    { name: 'Net Banking', icon: 'assets/images/payment/netbanking.svg' }
  ];
  
  socialLinks = [
    { name: 'Facebook', icon: 'facebook', url: 'https://www.facebook.com/HeadphoneZone' },
    { name: 'Instagram', icon: 'photo_camera', url: 'https://www.instagram.com/headphonezone/' },
    { name: 'Twitter', icon: 'alternate_email', url: 'https://twitter.com/Headphone_Zone' },
    { name: 'YouTube', icon: 'smart_display', url: 'https://www.youtube.com/c/HeadphoneZoneTV' }
  ];
}
