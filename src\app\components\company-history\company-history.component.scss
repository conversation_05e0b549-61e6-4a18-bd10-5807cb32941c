.company-history-section {
  padding: 4rem 0;
  background-color: #f9f9f9;
}

.history-container {
  max-width: 1200px;
  margin: 0 auto;
  padding: 0 1.5rem;
}

.section-header {
  text-align: center;
  margin-bottom: 3rem;
}

.team-photo {
  width: 100%;
  max-width: 800px;
  border-radius: 8px;
  margin-bottom: 1.5rem;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
}

.section-title {
  font-size: 2.5rem;
  font-weight: 700;
  color: #333;
  margin-bottom: 1rem;
}

.timeline-container {
  position: relative;
}

.timeline-navigation {
  display: flex;
  align-items: center;
  justify-content: center;
  margin-bottom: 2rem;
}

.prev-button,
.next-button {
  background: none;
  border: none;
  font-size: 1.5rem;
  color: #333;
  cursor: pointer;
  padding: 0.5rem;
  transition: all 0.3s ease;

  &:hover {
    color: #ff5722;
  }
}

.timeline-years {
  display: flex;
  justify-content: space-between;
  width: 80%;
  max-width: 800px;
  position: relative;
  margin: 0 1rem;

  &::before {
    content: '';
    position: absolute;
    top: 50%;
    left: 0;
    right: 0;
    height: 2px;
    background-color: #ddd;
    z-index: 0;
  }
}

.year-marker {
  position: relative;
  padding: 0.5rem;
  background-color: #fff;
  border: 2px solid #ddd;
  border-radius: 50%;
  font-weight: 600;
  font-size: 0.9rem;
  color: #666;
  cursor: pointer;
  transition: all 0.3s ease;
  z-index: 1;

  &.active {
    background-color: #ff5722;
    color: white;
    border-color: #ff5722;
    transform: scale(1.2);
  }

  &:hover:not(.active) {
    background-color: #f0f0f0;
    border-color: #ccc;
  }
}

.milestone-content {
  display: flex;
  flex-wrap: wrap;
  gap: 2rem;
  align-items: center;
  background-color: white;
  padding: 2rem;
  border-radius: 8px;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.05);
}

.milestone-image {
  flex: 1;
  min-width: 300px;

  img {
    width: 100%;
    border-radius: 8px;
    box-shadow: 0 4px 8px rgba(0, 0, 0, 0.1);
  }
}

.milestone-text {
  flex: 1;
  min-width: 300px;
}

.milestone-year {
  font-size: 2.5rem;
  font-weight: 700;
  color: #ff5722;
  margin-bottom: 0.5rem;
}

.milestone-title {
  font-size: 1.5rem;
  font-weight: 600;
  color: #333;
  margin-bottom: 1rem;
}

.milestone-description {
  font-size: 1rem;
  line-height: 1.6;
  color: #666;
}

@media (max-width: 768px) {
  .section-title {
    font-size: 2rem;
  }

  .timeline-years {
    width: 100%;
    overflow-x: auto;
    padding: 0.5rem 0;
    justify-content: flex-start;
    gap: 1rem;

    &::before {
      display: none;
    }
  }

  .year-marker {
    flex-shrink: 0;
  }

  .milestone-content {
    flex-direction: column;
  }

  .milestone-image,
  .milestone-text {
    width: 100%;
  }
}

@media (max-width: 576px) {
  .section-title {
    font-size: 1.8rem;
  }

  .milestone-year {
    font-size: 2rem;
  }

  .milestone-title {
    font-size: 1.3rem;
  }
}