import { Component, OnInit, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, NgZone } from '@angular/core';
import { CommonModule } from '@angular/common';
import { RouterModule } from '@angular/router';
import { MaterialModule } from '../../material/material.module';
import { interval, Subscription, BehaviorSubject, timer } from 'rxjs';
import { trigger, transition, style, animate } from '@angular/animations';

interface Banner {
  id: number;
  title: string;
  subtitle: string;
  imageUrl: string;
  buttonText: string;
  buttonLink: string;
}

@Component({
  selector: 'app-banner-slider',
  standalone: true,
  imports: [CommonModule, RouterModule, MaterialModule],
  templateUrl: './banner-slider.component.html',
  styleUrl: './banner-slider.component.scss',
  animations: [
    trigger('slideAnimation', [
      transition(':increment', [
        style({ transform: 'translateX(100%)', opacity: 0 }),
        animate(
          '500ms ease-out',
          style({ transform: 'translateX(0)', opacity: 1 })
        ),
      ]),
      transition(':decrement', [
        style({ transform: 'translateX(-100%)', opacity: 0 }),
        animate(
          '500ms ease-out',
          style({ transform: 'translateX(0)', opacity: 1 })
        ),
      ]),
    ]),
  ],
})
export class BannerSliderComponent implements OnInit, OnDestroy {
  // Custom cursor properties
  cursorX = 0;
  cursorY = 0;
  isCustomCursorVisible = false;
  timerProgress = 0;
  private readonly timerCircumference = 2 * Math.PI * 18; // 2πr where r=18
  private timerAnimationFrame: number | null = null;
  private timerStartTime = 0;

  banners: Banner[] = [
    {
      id: 1,
      title: 'Headphone Zone x FiiO JD1',
      subtitle: 'Audiophile-grade sound at an affordable price',
      imageUrl: 'https://images.unsplash.com/photo-1590658268037-6bf12165a8df?w=1200&h=500&fit=crop',
      buttonText: 'SHOP NOW',
      buttonLink: '/products/headphone-zone-x-fiio-jd1',
    },
    {
      id: 2,
      title: 'Headphone Connect',
      subtitle: 'Experience the best headphones in person',
      imageUrl: 'https://images.unsplash.com/photo-1546435770-a3e426bf472b?w=1200&h=500&fit=crop',
      buttonText: 'Learn More',
      buttonLink: '/pages/headphone-connect',
    },
    {
      id: 3,
      title: 'Headphone Zone x Oriveti Blackbird',
      subtitle: 'Our latest collaboration',
      imageUrl: 'https://images.unsplash.com/photo-1505740420928-5e560c06d30e?w=1200&h=500&fit=crop',
      buttonText: 'Shop Now',
      buttonLink: '/products/headphone-zone-x-oriveti-blackbird',
    },
    {
      id: 4,
      title: "Headphone Zone x Tangzu Wan'er S.G",
      subtitle: 'Discover our special collaboration',
      imageUrl: 'https://images.unsplash.com/photo-1484704849700-f032a568e944?w=1200&h=500&fit=crop',
      buttonText: 'Shop Now',
      buttonLink: '/products/headphone-zone-x-tangzu-waner-s-g',
    },
    {
      id: 5,
      title: 'Headphone Zone x Oriveti OD200',
      subtitle: 'Premium sound, exceptional value',
      imageUrl: 'https://images.unsplash.com/photo-1612444530582-fc66183b16f7?w=1200&h=500&fit=crop',
      buttonText: 'Shop Now',
      buttonLink: '/products/headphone-zone-x-oriveti-od200',
    },
  ];

  private currentIndexSubject = new BehaviorSubject<number>(0);
  currentIndex$ = this.currentIndexSubject.asObservable();
  private autoSlideSubscription?: Subscription;
  private readonly slideInterval = 5000;
  isPaused = false;

  @HostListener('keydown.arrowLeft', ['$event'])
  onKeyLeft(event: KeyboardEvent) {
    event.preventDefault();
    this.prevBanner();
  }

  @HostListener('keydown.arrowRight', ['$event'])
  onKeyRight(event: KeyboardEvent) {
    event.preventDefault();
    this.nextBanner();
  }

  ngOnInit(): void {
    this.startAutoSlide();
    // Initialize timer progress
    this.timerProgress = this.timerCircumference;
  }

  ngOnDestroy(): void {
    this.stopAutoSlide();
    this.stopTimerAnimation();
  }

  get currentBanner(): Banner {
    return this.banners[this.currentIndexSubject.value];
  }

  nextBanner(): void {
    const nextIndex =
      (this.currentIndexSubject.value + 1) % this.banners.length;
    this.currentIndexSubject.next(nextIndex);
    this.resetAutoSlide();
  }

  prevBanner(): void {
    const prevIndex =
      (this.currentIndexSubject.value - 1 + this.banners.length) %
      this.banners.length;
    this.currentIndexSubject.next(prevIndex);
    this.resetAutoSlide();
  }

  goToBanner(index: number): void {
    if (index !== this.currentIndexSubject.value) {
      this.currentIndexSubject.next(index);
      this.resetAutoSlide();
    }
  }

  toggleAutoSlide(): void {
    this.isPaused = !this.isPaused;
    if (this.isPaused) {
      this.stopAutoSlide();
    } else {
      this.startAutoSlide();
    }
  }

  private startAutoSlide(): void {
    this.stopAutoSlide();
    this.autoSlideSubscription = interval(this.slideInterval).subscribe(() => {
      if (!this.isPaused) {
        this.nextBanner();
      }
    });
  }

  private stopAutoSlide(): void {
    if (this.autoSlideSubscription) {
      this.autoSlideSubscription.unsubscribe();
    }
  }

  private resetAutoSlide(): void {
    if (!this.isPaused) {
      this.startAutoSlide();
    }
    // Reset timer animation
    this.resetTimerAnimation();
  }

  // Custom cursor methods
  updateCursorPosition(event: MouseEvent): void {
    // Offset by half the cursor size to center it on the mouse pointer
    this.cursorX = event.clientX - 20;
    this.cursorY = event.clientY - 20;
  }

  showCustomCursor(): void {
    this.isCustomCursorVisible = true;
    this.startTimerAnimation();
  }

  hideCustomCursor(): void {
    this.isCustomCursorVisible = false;
    this.stopTimerAnimation();
  }

  private startTimerAnimation(): void {
    this.timerStartTime = Date.now();
    this.stopTimerAnimation(); // Clear any existing animation

    const animate = () => {
      const elapsed = Date.now() - this.timerStartTime;
      const progress = Math.min(elapsed / this.slideInterval, 1);

      // Calculate stroke-dashoffset (starts at circumference and goes to 0)
      this.timerProgress = this.timerCircumference * (1 - progress);

      if (progress < 1) {
        this.timerAnimationFrame = requestAnimationFrame(animate);
      }
    };

    this.timerAnimationFrame = requestAnimationFrame(animate);
  }

  private stopTimerAnimation(): void {
    if (this.timerAnimationFrame !== null) {
      cancelAnimationFrame(this.timerAnimationFrame);
      this.timerAnimationFrame = null;
    }
  }

  private resetTimerAnimation(): void {
    this.stopTimerAnimation();
    if (this.isCustomCursorVisible) {
      this.startTimerAnimation();
    }
  }
}
