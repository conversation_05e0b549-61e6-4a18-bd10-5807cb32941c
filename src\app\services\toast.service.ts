import { Injectable } from '@angular/core';
import { BehaviorSubject, Observable, Subject, timer } from 'rxjs';
import { PurchaseNotification } from '../models/product.model';
import { takeUntil } from 'rxjs/operators';

@Injectable({
  providedIn: 'root',
})
export class ToastService {
  private notificationsSubject =
    new BehaviorSubject<PurchaseNotification | null>(null);
  private isActiveSubject = new BehaviorSubject<boolean>(false);
  private currentTimer: any;
  private soundEnabled = true;

  notifications$ = this.notificationsSubject.asObservable();
  isActive$ = this.isActiveSubject.asObservable();

  constructor() {}

  private clearCurrentTimer(): void {
    if (this.currentTimer) {
      clearTimeout(this.currentTimer);
    }
  }

  private showNotification(
    notification: PurchaseNotification,
    duration: number = 5000
  ): void {
    this.clearCurrentTimer();
    this.notificationsSubject.next(notification);
    this.isActiveSubject.next(true);

    // Play sound if enabled
    if (this.soundEnabled) {
      this.playNotificationSound(notification.type || 'purchase');
    }

    this.currentTimer = setTimeout(() => {
      this.isActiveSubject.next(false);
      setTimeout(() => {
        this.notificationsSubject.next(null);
      }, 300); // Match animation duration
    }, duration);
  }

  showPurchase(
    customerName: string,
    location: string,
    productName: string
  ): void {
    this.showNotification({
      customerName,
      location,
      productName,
      timestamp: new Date(),
    });
  }

  showOrderConfirmation(orderId: string): void {
    this.showNotification({
      type: 'order-confirmation',
      productName: `Order #${orderId} confirmed`,
      additionalInfo: 'Thank you for your purchase!',
      timestamp: new Date(),
    });
  }

  showStockWarning(productName: string, stockCount: number): void {
    this.showNotification(
      {
        type: 'stock-warning',
        productName,
        additionalInfo: `Only ${stockCount} items left in stock!`,
        timestamp: new Date(),
      },
      8000
    ); // Show stock warnings for longer
  }

  showViewersNotification(productName: string, viewerCount: number): void {
    this.showNotification(
      {
        type: 'viewer',
        productName,
        viewerCount,
        timestamp: new Date(),
      },
      4000
    );
  }

  showCopySuccess(text: string): void {
    this.showNotification(
      {
        type: 'copy',
        productName: 'Copied to clipboard!',
        additionalInfo: text,
        timestamp: new Date(),
      },
      3000
    );
  }

  showError(message: string): void {
    this.showNotification(
      {
        type: 'error',
        productName: message,
        timestamp: new Date(),
      },
      6000
    );
  }

  showSuccess(message: string): void {
    this.showNotification(
      {
        type: 'success',
        productName: message,
        timestamp: new Date(),
      },
      3000
    );
  }

  showProductPurchaseNotification(productName: string): void {
    this.showNotification(
      {
        type: 'purchase',
        productName,
        additionalInfo: 'Added to cart successfully!',
        timestamp: new Date(),
      },
      3000
    );
  }

  toggleSound(): void {
    this.soundEnabled = !this.soundEnabled;
  }

  isSoundEnabled(): boolean {
    return this.soundEnabled;
  }

  private playNotificationSound(type: string): void {
    // Create different sounds for different notification types
    const context = new (window.AudioContext ||
      (window as any).webkitAudioContext)();
    const oscillator = context.createOscillator();
    const gainNode = context.createGain();

    oscillator.connect(gainNode);
    gainNode.connect(context.destination);

    // Configure sound based on notification type
    switch (type) {
      case 'purchase':
        oscillator.frequency.setValueAtTime(440, context.currentTime); // A4 note
        gainNode.gain.setValueAtTime(0.1, context.currentTime);
        break;
      case 'stock-warning':
        oscillator.frequency.setValueAtTime(330, context.currentTime); // E4 note
        gainNode.gain.setValueAtTime(0.2, context.currentTime);
        break;
      case 'viewer':
        oscillator.frequency.setValueAtTime(523.25, context.currentTime); // C5 note
        gainNode.gain.setValueAtTime(0.05, context.currentTime);
        break;
      default:
        oscillator.frequency.setValueAtTime(392, context.currentTime); // G4 note
        gainNode.gain.setValueAtTime(0.1, context.currentTime);
    }

    oscillator.start(context.currentTime);
    oscillator.stop(context.currentTime + 0.1);

    // Clean up
    setTimeout(() => {
      gainNode.disconnect();
      oscillator.disconnect();
    }, 200);
  }
}
