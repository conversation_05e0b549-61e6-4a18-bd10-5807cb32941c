.section-title {
  position: relative;
  font-size: 1.75rem;
  font-weight: 600;
  color: var(--text-primary);
  padding-bottom: 0.5rem;
  
  &::after {
    content: '';
    position: absolute;
    bottom: 0;
    left: 50%;
    transform: translateX(-50%);
    width: 60px;
    height: 3px;
    background-color: var(--primary);
    border-radius: 3px;
  }
}

.signature-selector {
  .signature-button {
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    padding: 1rem;
    min-width: 120px;
    border-radius: 8px;
    background-color: white;
    border: 2px solid transparent;
    transition: all 0.2s ease;
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.05);
    
    .icon {
      font-size: 1.75rem;
      margin-bottom: 0.5rem;
    }
    
    .name {
      font-weight: 500;
      font-size: 0.9rem;
    }
    
    &:hover {
      transform: translateY(-2px);
      box-shadow: 0 4px 8px rgba(0, 0, 0, 0.1);
    }
    
    &.active {
      border-color: var(--primary);
      background-color: rgba(var(--primary-rgb), 0.05);
      
      .name {
        color: var(--primary);
      }
    }
  }
}

.signature-details {
  transition: all 0.3s ease;
  animation: fadeIn 0.3s ease;
  
  .product-card {
    transition: all 0.2s ease;
    
    &:hover {
      transform: translateY(-2px);
    }
  }
}

@keyframes fadeIn {
  from {
    opacity: 0;
    transform: translateY(10px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

// Responsive adjustments
@media (max-width: 768px) {
  .signature-selector {
    .signature-button {
      min-width: 100px;
      padding: 0.75rem;
      
      .icon {
        font-size: 1.5rem;
      }
      
      .name {
        font-size: 0.8rem;
      }
    }
  }
  
  .signature-details {
    .grid {
      grid-template-columns: 1fr;
    }
  }
}
