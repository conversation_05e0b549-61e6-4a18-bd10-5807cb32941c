import { Component, OnInit } from '@angular/core';
import { CommonModule } from '@angular/common';
import { RouterModule } from '@angular/router';
import { MaterialModule } from '../../material/material.module';
import {
  FormsModule,
  ReactiveFormsModule,
  FormGroup,
  FormBuilder,
  Validators,
} from '@angular/forms';

interface Event {
  id: number;
  title: string;
  date: Date;
  location: string;
  city: string;
  imageUrl: string;
  description: string;
  registrationOpen: boolean;
  ticketPrice?: number;
  ticketsAvailable: number;
}

@Component({
  selector: 'app-headphone-connect',
  standalone: true,
  imports: [
    CommonModule,
    RouterModule,
    MaterialModule,
    FormsModule,
    ReactiveFormsModule,
  ],
  templateUrl: './headphone-connect.component.html',
  styleUrl: './headphone-connect.component.scss',
})
export class HeadphoneConnectComponent implements OnInit {
  upcomingEvents: Event[] = [
    {
      id: 1,
      title: 'Headphone Connect Mumbai',
      date: new Date('2023-12-15T18:00:00'),
      location: 'The Bombay Canteen',
      city: 'Mumbai',
      imageUrl:
        '//www.headphonezone.in/cdn/shop/files/Headphone-Connect-Mumbai-22.jpg?v=1730886762',
      description:
        'Join us for an evening of music, craft beer, and the finest headphones. Experience high-end audio gear from brands like Focal, Audeze, and more.',
      registrationOpen: true,
      ticketPrice: 499,
      ticketsAvailable: 25,
    },
    {
      id: 2,
      title: 'Headphone Connect Bangalore',
      date: new Date('2023-12-22T18:00:00'),
      location: 'Toit Brewpub',
      city: 'Bangalore',
      imageUrl:
        '//www.headphonezone.in/cdn/shop/files/Headphone-Zone-Bengaluru.jpg?v=1731064060',
      description:
        'Experience the best headphones and IEMs in a relaxed setting with fellow audiophiles. Our experts will be there to guide you through your listening journey.',
      registrationOpen: true,
      ticketPrice: 499,
      ticketsAvailable: 30,
    },
    {
      id: 3,
      title: 'Headphone Connect Delhi',
      date: new Date('2024-01-12T18:00:00'),
      location: 'Sidecar',
      city: 'Delhi',
      imageUrl:
        '//www.headphonezone.in/cdn/shop/files/Headphone-Connect-Delhi-19.jpg?v=1730886609',
      description:
        "Delhi audiophiles, this one's for you! Join us for an evening of amazing music through the finest headphones, accompanied by great food and drinks.",
      registrationOpen: true,
      ticketPrice: 499,
      ticketsAvailable: 20,
    },
  ];

  selectedEvent: Event | null = null;
  registrationForm: FormGroup;
  registrationSuccess = false;

  constructor(private fb: FormBuilder) {
    this.registrationForm = this.fb.group({
      name: ['', [Validators.required]],
      email: ['', [Validators.required, Validators.email]],
      phone: ['', [Validators.required, Validators.pattern('^[0-9]{10}$')]],
      quantity: [
        1,
        [Validators.required, Validators.min(1), Validators.max(5)],
      ],
      agreeTerms: [false, [Validators.requiredTrue]],
    });
  }

  ngOnInit(): void {}

  openRegistrationModal(event: Event): void {
    this.selectedEvent = event;
    this.registrationForm.reset({
      name: '',
      email: '',
      phone: '',
      quantity: 1,
      agreeTerms: false,
    });
    this.registrationSuccess = false;
  }

  closeRegistrationModal(): void {
    this.selectedEvent = null;
  }

  registerForEvent(): void {
    if (this.registrationForm.valid && this.selectedEvent) {
      // In a real app, you would send this data to a backend service
      console.log('Registration submitted:', {
        event: this.selectedEvent.title,
        ...this.registrationForm.value,
      });

      // Show success message
      this.registrationSuccess = true;

      // Reset form after submission
      setTimeout(() => {
        this.closeRegistrationModal();
      }, 3000);
    } else {
      // Mark all fields as touched to trigger validation messages
      this.registrationForm.markAllAsTouched();
    }
  }

  formatDate(date: Date): string {
    return date.toLocaleDateString('en-US', {
      weekday: 'long',
      year: 'numeric',
      month: 'long',
      day: 'numeric',
    });
  }

  formatTime(date: Date): string {
    return date.toLocaleTimeString('en-US', {
      hour: '2-digit',
      minute: '2-digit',
    });
  }

  getTicketTotal(): number {
    if (!this.selectedEvent || !this.selectedEvent.ticketPrice) return 0;
    const quantity = this.registrationForm.get('quantity')?.value || 0;
    return this.selectedEvent.ticketPrice * quantity;
  }
}
