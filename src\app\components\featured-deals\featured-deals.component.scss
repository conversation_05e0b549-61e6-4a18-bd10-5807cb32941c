.featured-deals {
  background-color: #fff;

  .section-title {
    position: relative;
    font-weight: 700;
    color: #333;

    &::after {
      content: '';
      position: absolute;
      bottom: -10px;
      left: 50%;
      transform: translateX(-50%);
      width: 50px;
      height: 3px;
      background-color: var(--primary);
      border-radius: 3px;
    }
  }
}

// Product Card Styling
.product-card {
  position: relative;
  border: 1px solid #e5e7eb;
  border-radius: 8px;
  overflow: hidden;
  transition: all 0.3s ease;
  background-color: #fff;

  &:hover {
    box-shadow: 0 10px 20px rgba(0, 0, 0, 0.1);
    transform: translateY(-5px);
  }

  // Product Badge
  .product-badge {
    font-size: 0.7rem;
    font-weight: 600;
    letter-spacing: 0.5px;
    border-radius: 4px;
  }

  // Product Image
  .product-image {
    position: relative;
    padding: 1rem;
    background-color: #f9fafb;
    aspect-ratio: 1/1;
    display: flex;
    align-items: center;
    justify-content: center;

    img {
      max-height: 100%;
      object-fit: contain;
      transition: transform 0.5s ease;
    }

    &:hover img {
      transform: scale(1.05);
    }
  }

  // Quick Actions
  .quick-actions {
    button {
      width: 36px;
      height: 36px;

      ::ng-deep .mat-mdc-button-touch-target {
        width: 36px;
        height: 36px;
      }

      mat-icon {
        font-size: 18px;
        height: 18px;
        width: 18px;
        line-height: 18px;
      }
    }
  }

  // Product Info
  .product-info {
    padding: 1rem;

    .product-title {
      font-weight: 500;
      font-size: 1rem;
      margin-bottom: 0.5rem;
      line-height: 1.4;
      display: -webkit-box;
      -webkit-line-clamp: 2;
      -webkit-box-orient: vertical;
      overflow: hidden;
      color: #1f2937;
    }

    // Price
    .product-price {
      display: flex;
      flex-wrap: wrap;
      align-items: baseline;
      gap: 0.5rem;
      margin: 0.5rem 0;

      .original-price {
        color: #9ca3af;
        text-decoration: line-through;
        font-size: 0.875rem;
      }

      .current-price {
        color: var(--primary);
        font-weight: 600;
        font-size: 1.125rem;
      }

      .discount {
        color: #10b981;
        font-size: 0.75rem;
        font-weight: 500;
      }
    }

    // Stock Status
    .text-error {
      color: #ef4444;
    }

    .text-warning {
      color: #f59e0b;
    }

    .text-success {
      color: #10b981;
    }

    // Add to Cart Button
    button {
      background-color: var(--primary);
      border: none;
      font-weight: 600;
      letter-spacing: 0.5px;
      transition: all 0.3s ease;

      &:hover:not([disabled]) {
        background-color: darken(#ff5500, 5%);
        transform: translateY(-2px);
      }

      &[disabled] {
        background-color: #d1d5db;
        color: #6b7280;
        cursor: not-allowed;
      }
    }
  }
}

// View All Button
.btn-outline {
  position: relative;
  overflow: hidden;
  z-index: 1;
  transition: all 0.3s ease;

  &::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    width: 0;
    height: 100%;
    background-color: var(--primary);
    z-index: -1;
    transition: width 0.3s ease;
  }

  &:hover {
    color: white;

    &::before {
      width: 100%;
    }

    mat-icon {
      transform: translateX(4px);
    }
  }

  mat-icon {
    transition: transform 0.3s ease;
  }
}

// Animation for staggered appearance
@for $i from 1 through 8 {
  .product-card:nth-child(#{$i}) {
    animation-delay: #{$i * 0.1}s;
  }
}

// Responsive adjustments
@media (max-width: 768px) {
  .product-card {
    .product-image {
      aspect-ratio: 4/3;
    }

    &:hover {
      transform: translateY(-3px);
    }
  }
}

// Reduced motion preferences
@media (prefers-reduced-motion: reduce) {
  .product-card,
  .product-image img,
  .btn-outline::before,
  .btn-outline mat-icon {
    transition: none !important;
    animation: none !important;
  }
}