<div class="auth-container">
  <div class="auth-card">
    <div class="auth-header">
      <h1 class="auth-title">Create Account</h1>
      <p class="auth-subtitle">Join the Headphone Zone community</p>
    </div>

    <form [formGroup]="registerForm" (ngSubmit)="onSubmit()" class="auth-form">
      <div class="name-fields">
        <mat-form-field appearance="outline" class="form-field">
          <mat-label>First Name</mat-label>
          <input matInput type="text" formControlName="firstName">
          <mat-error *ngIf="registerForm.get('firstName')?.invalid">{{ getErrorMessage('firstName') }}</mat-error>
        </mat-form-field>

        <mat-form-field appearance="outline" class="form-field">
          <mat-label>Last Name</mat-label>
          <input matInput type="text" formControlName="lastName">
          <mat-error *ngIf="registerForm.get('lastName')?.invalid">{{ getErrorMessage('lastName') }}</mat-error>
        </mat-form-field>
      </div>

      <mat-form-field appearance="outline" class="form-field">
        <mat-label>Email</mat-label>
        <input matInput type="email" formControlName="email" placeholder="<EMAIL>">
        <mat-error *ngIf="registerForm.get('email')?.invalid">{{ getErrorMessage('email') }}</mat-error>
      </mat-form-field>

      <mat-form-field appearance="outline" class="form-field">
        <mat-label>Password</mat-label>
        <input matInput [type]="hidePassword ? 'password' : 'text'" formControlName="password">
        <button mat-icon-button matSuffix type="button" (click)="hidePassword = !hidePassword">
          <mat-icon>{{ hidePassword ? 'visibility_off' : 'visibility' }}</mat-icon>
        </button>
        <mat-error *ngIf="registerForm.get('password')?.invalid">{{ getErrorMessage('password') }}</mat-error>
        <mat-hint>Password must be at least 6 characters long</mat-hint>
      </mat-form-field>

      <div class="terms-checkbox">
        <mat-checkbox formControlName="acceptTerms" color="primary">
          I agree to the <a href="/pages/terms-and-conditions" target="_blank">Terms & Conditions</a> and <a href="/pages/privacy-policy" target="_blank">Privacy Policy</a>
        </mat-checkbox>
        <mat-error *ngIf="registerForm.get('acceptTerms')?.invalid && registerForm.get('acceptTerms')?.touched" class="terms-error">
          {{ getErrorMessage('acceptTerms') }}
        </mat-error>
      </div>

      <button mat-raised-button color="primary" type="submit" class="submit-button" [disabled]="isLoading">
        <mat-spinner *ngIf="isLoading" diameter="20" class="spinner"></mat-spinner>
        <span *ngIf="!isLoading">Create Account</span>
      </button>

      <div class="social-login">
        <p class="divider"><span>or continue with</span></p>
        <div class="social-buttons">
          <button mat-stroked-button type="button" class="social-button">
            <img src="assets/images/icons/google.svg" alt="Google" class="social-icon">
            Google
          </button>
          <button mat-stroked-button type="button" class="social-button">
            <img src="assets/images/icons/facebook.svg" alt="Facebook" class="social-icon">
            Facebook
          </button>
        </div>
      </div>
    </form>

    <div class="auth-footer">
      <p>Already have an account? <a routerLink="/account/login" class="login-link">Login</a></p>
    </div>
  </div>
</div>
