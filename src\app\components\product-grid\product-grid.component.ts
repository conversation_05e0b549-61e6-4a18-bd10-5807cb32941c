import { Component, OnInit, HostListener } from '@angular/core';
import { CommonModule } from '@angular/common';
import { RouterModule } from '@angular/router';
import { MaterialModule } from '../../material/material.module';
import {
  FormsModule,
  ReactiveFormsModule,
  FormControl,
  FormGroup,
} from '@angular/forms';
import { ProductService } from '../../services/product.service';
import { CartService } from '../../services/cart.service';
import { ToastService } from '../../services/toast.service';
import { Product } from '../../models/product.model';
import { Observable, BehaviorSubject, combineLatest } from 'rxjs';
import { ProductBadgeComponent } from '../product-badge/product-badge.component';
import { SoundSignatureIndicatorComponent } from '../sound-signature-indicator/sound-signature-indicator.component';
import {
  map,
  debounceTime,
  distinctUntilChanged,
  startWith,
  tap,
  take,
} from 'rxjs/operators';
import {
  trigger,
  transition,
  style,
  animate,
  query,
  stagger,
} from '@angular/animations';

interface FilterState {
  category: string;
  priceRange: [number, number];
  rating: number;
  availability: boolean;
  onSale: boolean;
}

type SortOption = 'price-asc' | 'price-desc' | 'rating' | 'newest' | 'featured';

@Component({
  selector: 'app-product-grid',
  standalone: true,
  imports: [
    CommonModule,
    RouterModule,
    MaterialModule,
    FormsModule,
    ReactiveFormsModule,
    ProductBadgeComponent,
    SoundSignatureIndicatorComponent,
  ],
  templateUrl: './product-grid.component.html',
  styleUrl: './product-grid.component.scss',
  animations: [
    trigger('staggerAnimation', [
      transition('* => *', [
        query(
          ':enter',
          [
            style({ opacity: 0, transform: 'translateY(20px)' }),
            stagger(50, [
              animate(
                '0.3s ease',
                style({ opacity: 1, transform: 'translateY(0)' })
              ),
            ]),
          ],
          { optional: true }
        ),
      ]),
    ]),
  ],
})
export class ProductGridComponent implements OnInit {
  products$!: Observable<Product[]>;
  filteredProducts$!: Observable<Product[]>;
  isSearchActive = false;
  isLoading = false;
  displayedProducts: Product[] = [];
  currentPage = 1;
  itemsPerPage = 12;

  // Make Math available in the template
  Math = Math;

  categories = new Set<string>();
  maxPrice = 0;
  minPrice = 0;

  filterForm = new FormGroup({
    category: new FormControl('all'),
    priceRange: new FormControl([0, 2000] as [number, number]),
    rating: new FormControl(0),
    availability: new FormControl(false),
    onSale: new FormControl(false),
  });

  sortControl = new FormControl<SortOption>('newest');
  private filterState = new BehaviorSubject<FilterState>({
    category: 'all',
    priceRange: [0, 2000],
    rating: 0,
    availability: false,
    onSale: false,
  });

  private hasMoreProducts = true;
  private isLoadingMore = false;

  constructor(
    private productService: ProductService,
    private cartService: CartService,
    private toastService: ToastService
  ) {}

  ngOnInit(): void {
    this.products$ = this.productService.getProducts().pipe(
      tap((products: Product[]) => {
        this.updateCategoriesAndPriceRange(products);
      })
    );

    // Fix the filtered products stream
    this.filteredProducts$ = combineLatest([
      this.products$,
      this.filterForm.valueChanges.pipe(
        startWith(this.filterForm.value),
        map((formValue) => ({
          category: formValue?.category || 'all',
          priceRange: (formValue?.priceRange as [number, number]) || [0, 2000],
          rating: formValue?.rating || 0,
          availability: formValue?.availability || false,
          onSale: formValue?.onSale || false,
        }))
      ),
      this.sortControl.valueChanges.pipe(startWith(this.sortControl.value)),
    ]).pipe(
      map(([products, filters, sortOption]) =>
        this.filterAndSortProducts(products, filters, sortOption || 'newest')
      )
    );

    this.loadMoreProducts();
  }

  private updateCategoriesAndPriceRange(products: Product[]): void {
    products.forEach((product) => {
      this.categories.add(product.category);
      this.maxPrice = Math.max(this.maxPrice, product.price);
      this.minPrice = Math.min(this.minPrice, product.price);
    });

    this.filterForm.patchValue({
      priceRange: [this.minPrice, this.maxPrice],
    });
  }

  private setupFiltering(): void {
    this.filterForm.valueChanges
      .pipe(debounceTime(200))
      .subscribe((formValue) => {
        const priceRange = formValue.priceRange
          ? ([
              formValue.priceRange[0] || 0,
              formValue.priceRange[1] || 2000,
            ] as [number, number])
          : ([0, 2000] as [number, number]);

        this.filterState.next({
          category: formValue.category || 'all',
          priceRange: priceRange,
          rating: formValue.rating || 0,
          availability: formValue.availability || false,
          onSale: formValue.onSale || false,
        });
        this.resetPagination();
      });
  }

  private filterAndSortProducts(
    products: Product[],
    filters: FilterState,
    sortOption: SortOption
  ): Product[] {
    let filtered = products.filter((product) => {
      const matchesCategory =
        filters.category === 'all' || product.category === filters.category;
      const matchesPrice =
        product.price >= filters.priceRange[0] &&
        product.price <= filters.priceRange[1];
      const matchesRating = product.rating >= filters.rating;
      const matchesAvailability = !filters.availability || product.stock > 0;
      const matchesSale =
        !filters.onSale || product.discountedPrice !== undefined;

      return (
        matchesCategory &&
        matchesPrice &&
        matchesRating &&
        matchesAvailability &&
        matchesSale
      );
    });

    // Apply sorting
    filtered = this.sortProducts(filtered, sortOption);
    return filtered;
  }

  private sortProducts(products: Product[], sortOption: SortOption): Product[] {
    switch (sortOption) {
      case 'price-asc':
        return [...products].sort(
          (a, b) =>
            (a.discountedPrice || a.price) - (b.discountedPrice || b.price)
        );
      case 'price-desc':
        return [...products].sort(
          (a, b) =>
            (b.discountedPrice || b.price) - (a.discountedPrice || a.price)
        );
      case 'rating':
        return [...products].sort((a, b) => b.rating - a.rating);
      case 'newest':
        return [...products].sort(
          (a, b) => (b.isNew ? 1 : 0) - (a.isNew ? 1 : 0)
        );
      case 'featured':
        return [...products].sort(
          (a, b) => (b.isHot ? 2 : 0) + (b.isNew ? 1 : 0) - ((a.isHot ? 2 : 0) + (a.isNew ? 1 : 0))
        );
      default:
        return products;
    }
  }

  // Method to reset all filters
  resetFilters(): void {
    this.filterForm.reset({
      category: 'all',
      priceRange: [this.minPrice, this.maxPrice],
      rating: 0,
      availability: false,
      onSale: false
    });
    this.resetPagination();
  }

  // Method to set rating filter
  setRatingFilter(rating: number): void {
    this.filterForm.patchValue({
      rating: rating
    });
  }

  @HostListener('window:scroll', ['$event'])
  onScroll(): void {
    if (this.isLoadingMore || !this.hasMoreProducts) return;

    const windowHeight = window.innerHeight;
    const documentHeight = document.documentElement.scrollHeight;
    const scrollTop = window.pageYOffset || document.documentElement.scrollTop;

    if (windowHeight + scrollTop >= documentHeight - 1000) {
      this.loadMoreProducts();
    }
  }

  loadMoreProducts(): void {
    if (this.isLoadingMore || !this.hasMoreProducts) return;

    this.isLoadingMore = true;
    this.filteredProducts$.pipe(take(1)).subscribe((allProducts) => {
      const startIndex = (this.currentPage - 1) * this.itemsPerPage;
      const endIndex = startIndex + this.itemsPerPage;

      const newProducts = allProducts.slice(startIndex, endIndex);

      if (newProducts.length > 0) {
        this.displayedProducts = [...this.displayedProducts, ...newProducts];
        this.currentPage++;
        this.hasMoreProducts = endIndex < allProducts.length;
      } else {
        this.hasMoreProducts = false;
      }

      this.isLoadingMore = false;
    });
  }

  resetPagination(): void {
    this.currentPage = 1;
    this.displayedProducts = [];
    this.hasMoreProducts = true;
    this.loadMoreProducts();
  }

  getDiscountPercentage(product: Product): number {
    if (!product.discountedPrice) return 0;
    return Math.round(
      ((product.price - product.discountedPrice) / product.price) * 100
    );
  }

  addToCart(product: Product, event: Event): void {
    event.preventDefault();
    event.stopPropagation();
    this.cartService.addToCart(product);
    this.toastService.showProductPurchaseNotification(product.name);
  }

  showViewersNotification(product: Product): void {
    const viewerCount = Math.floor(Math.random() * 10) + 1;
    this.toastService.showViewersNotification(product.name, viewerCount);
  }

  getStockStatus(stock: number): 'in-stock' | 'low-stock' | 'out-of-stock' {
    if (stock === 0) return 'out-of-stock';
    if (stock <= 5) return 'low-stock';
    return 'in-stock';
  }
}
