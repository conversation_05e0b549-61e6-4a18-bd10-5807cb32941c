import { ComponentFixture, TestBed } from '@angular/core/testing';

import { HeadphoneConnectComponent } from './headphone-connect.component';

describe('HeadphoneConnectComponent', () => {
  let component: HeadphoneConnectComponent;
  let fixture: ComponentFixture<HeadphoneConnectComponent>;

  beforeEach(async () => {
    await TestBed.configureTestingModule({
      imports: [HeadphoneConnectComponent]
    })
    .compileComponents();

    fixture = TestBed.createComponent(HeadphoneConnectComponent);
    component = fixture.componentInstance;
    fixture.detectChanges();
  });

  it('should create', () => {
    expect(component).toBeTruthy();
  });
});
