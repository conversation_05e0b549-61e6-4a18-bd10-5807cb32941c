import { Component } from '@angular/core';
import { CommonModule } from '@angular/common';
import { RouterModule } from '@angular/router';
import { MaterialModule } from '../../material/material.module';

interface Category {
  name: string;
  image: string;
  description: string;
  link: string;
  buttonText?: string;
}

@Component({
  selector: 'app-category-showcase',
  standalone: true,
  imports: [CommonModule, RouterModule, MaterialModule],
  templateUrl: './category-showcase.component.html',
  styleUrls: ['./category-showcase.component.scss'],
})
export class CategoryShowcaseComponent {
  categories: Category[] = [
    {
      name: 'Our Demo Units',
      image: 'https://placehold.co/800x1000/333333/FFFFFF?text=Demo+Units',
      description: 'Experience-tested audiophile gear at special prices',
      link: '/collections/demo-and-refurbished',
      buttonText: 'Shop Now',
    },
    {
      name: 'Unboxed',
      image: 'https://placehold.co/800x1000/333333/FFFFFF?text=Unboxed',
      description: 'Like-new products with full warranty at great discounts',
      link: '/collections/unboxed',
      buttonText: 'Shop Now',
    },
    {
      name: 'Deals of the Month',
      image:
        'https://placehold.co/800x1000/333333/FFFFFF?text=Deals+of+the+Month',
      description: 'Limited-time offers on premium audio gear',
      link: '/pages/deals-of-the-month',
      buttonText: 'Shop Now',
    },
  ];
}
