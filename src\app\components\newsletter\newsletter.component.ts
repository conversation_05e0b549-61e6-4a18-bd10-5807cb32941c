import { Component } from '@angular/core';
import { CommonModule } from '@angular/common';
import { MaterialModule } from '../../material/material.module';
import { FormsModule, ReactiveFormsModule, FormGroup, FormControl, Validators } from '@angular/forms';
import { ToastService } from '../../services/toast.service';

@Component({
  selector: 'app-newsletter',
  standalone: true,
  imports: [
    CommonModule,
    MaterialModule,
    FormsModule,
    ReactiveFormsModule
  ],
  templateUrl: './newsletter.component.html',
  styleUrls: ['./newsletter.component.scss']
})
export class NewsletterComponent {
  newsletterForm = new FormGroup({
    email: new FormControl('', [Validators.required, Validators.email])
  });
  
  isSubmitting = false;
  
  constructor(private toastService: ToastService) {}
  
  onSubmit(): void {
    if (this.newsletterForm.invalid) {
      return;
    }
    
    this.isSubmitting = true;
    
    // Simulate API call
    setTimeout(() => {
      this.isSubmitting = false;
      this.toastService.showSuccess('Thank you for subscribing to our newsletter!');
      this.newsletterForm.reset();
    }, 1500);
  }
}
