<header class="fixed top-0 left-0 right-0 z-50 bg-white border-b border-gray-200 transition-all duration-300"
  [class.shadow-sm]="isScrolled">
  <!-- Top Bar -->
  <div class="bg-gray-800 text-white py-1 text-xs hidden md:block">
    <div class="container mx-auto px-4">
      <div class="flex justify-between items-center">
        <div class="flex items-center space-x-4">
          <a href="tel:+************" class="hover:text-gray-200 transition-colors flex items-center">
            <span>+91 98765 43210</span>
          </a>
          <a href="mailto:<EMAIL>" class="hover:text-gray-200 transition-colors flex items-center">
            <span>support&#64;headphonezone.in</span>
          </a>
        </div>
        <div class="flex items-center space-x-4">
          <a href="#" class="hover:text-gray-200 transition-colors">Track Order</a>
          <a href="#" class="hover:text-gray-200 transition-colors">Help Center</a>
          <a href="#" class="hover:text-gray-200 transition-colors">FAQ</a>
          <a href="#" class="hover:text-gray-200 transition-colors">Blog</a>
        </div>
      </div>
    </div>
  </div>

  <!-- Main Header -->
  <div class="container mx-auto px-4">
    <div class="flex items-center justify-between h-16">
      <!-- Logo -->
      <a routerLink="/" class="flex items-center space-x-2">
        <img src="//www.headphonezone.in/cdn/shop/files/Headphone-Zone-Logo.png?v=1691395473" alt="Headphone Zone" class="h-8">
      </a>

      <!-- Navigation -->
      <nav class="hidden lg:flex items-center space-x-6">
        <div *ngFor="let category of categories" class="relative nav-item" [class.active-dropdown]="activeCategory === category">

          <!-- Menu Item -->
          <ng-container *ngIf="!category.path; else directLink">
            <div class="flex items-center">
              <button class="flex items-center hover:text-primary transition-colors py-4 font-medium nav-button"
                [class.active]="activeCategory === category">
                <span>{{ category.name }}</span>
              </button>
              <div class="ml-1 dropdown-arrow-container" (click)="toggleCategory(category, $event)">
                <span class="material-icons transform transition-transform dropdown-arrow"
                         [class.rotate-180]="activeCategory === category">expand_more</span>
              </div>
            </div>
          </ng-container>

          <ng-template #directLink>
            <div class="flex items-center">
              <a [href]="category.path" [target]="isExternalLink(category.path) ? '_blank' : '_self'"
                 class="flex items-center hover:text-primary transition-colors py-4 font-medium">
                <span>{{ category.name }}</span>
                <span *ngIf="isExternalLink(category.path)" class="material-icons text-sm ml-1">open_in_new</span>
              </a>
              <div *ngIf="category.subcategories" class="ml-1 dropdown-arrow-container" (click)="toggleCategory(category, $event)">
                <span class="material-icons transform transition-transform dropdown-arrow"
                         [class.rotate-180]="activeCategory === category">expand_more</span>
              </div>
            </div>
          </ng-template>

          <!-- Shop By Category Dropdown -->
          <div *ngIf="category.name === 'Categories' && activeCategory === category" class="category-dropdown fixed left-0 right-0 top-[64px] bg-white shadow-lg z-50 border border-gray-200">
            <div class="p-4">
              <div class="grid grid-cols-8 gap-4">
                <div class="col-span-1">
                  <h3 class="font-medium mb-2">In-Ears</h3>
                  <ul class="space-y-1">
                    <li><a href="/collections/beginner-audiophile-iems" class="text-sm hover:text-primary">Beginner Audiophile IEMs</a></li>
                    <li><a href="/collections/flagship-audiophile-iems" class="text-sm hover:text-primary">Flagship Audiophile IEMs</a></li>
                    <li><a href="/collections/our-collabs" class="text-sm hover:text-primary">Our Collabs</a></li>
                    <li><a href="/collections/type-c-in-ear-monitors" class="text-sm hover:text-primary">Type-C IEMs</a></li>
                    <li><a href="/collections/on-stage-monitors" class="text-sm hover:text-primary">On-Stage Monitors</a></li>
                    <li><a href="/collections/iem-cables" class="text-sm hover:text-primary">IEM Cables</a></li>
                    <li><a href="/collections/eartips" class="text-sm hover:text-primary">Eartips</a></li>
                    <li><a href="/collections/iems-bundles" class="text-sm hover:text-primary">IEMs Bundles</a></li>
                  </ul>
                </div>
                <div class="col-span-1">
                  <h3 class="font-medium mb-2">Headphones</h3>
                  <ul class="space-y-1">
                    <li><a href="/collections/beginner-audiophile-headphones" class="text-sm hover:text-primary">Beginner Audiophile</a></li>
                    <li><a href="/collections/flagship-audiophile-headphones" class="text-sm hover:text-primary">Flagship Headphones</a></li>
                    <li><a href="/collections/studio-professional-headphones" class="text-sm hover:text-primary">Studio & Professional</a></li>
                    <li><a href="/collections/work-from-home-headphones" class="text-sm hover:text-primary">For Calls</a></li>
                    <li><a href="/collections/gaming-headphones" class="text-sm hover:text-primary">Gaming</a></li>
                    <li><a href="/collections/headphone-cables" class="text-sm hover:text-primary">Headphone Cables</a></li>
                    <li><a href="/collections/earpads" class="text-sm hover:text-primary">Earpads</a></li>
                    <li><a href="/collections/headphones-bundles" class="text-sm hover:text-primary">Headphones Bundles</a></li>
                  </ul>
                </div>
                <div class="col-span-1">
                  <h3 class="font-medium mb-2">DACs & Amps</h3>
                  <ul class="space-y-1">
                    <li><a href="/collections/portable-amps-dacs" class="text-sm hover:text-primary">Portable DACs & Amps</a></li>
                    <li><a href="/collections/desktop-amps-dacs" class="text-sm hover:text-primary">Desktop DACs & Amps</a></li>
                    <li><a href="/collections/speaker-amps" class="text-sm hover:text-primary">Speaker Amps</a></li>
                    <li><a href="/collections/network-streamers" class="text-sm hover:text-primary">Network Streamers</a></li>
                  </ul>
                </div>
                <div class="col-span-1">
                  <h3 class="font-medium mb-2">Hi-Res Audio Players</h3>
                  <ul class="space-y-1">
                    <li><a href="/collections/portable-hi-res-digital-audio-player-daps" class="text-sm hover:text-primary">Portable Hi-Res Players</a></li>
                    <li><a href="/collections/desktop-hi-res-digital-audio-player-daps" class="text-sm hover:text-primary">Desktop Hi-Res Players</a></li>
                    <li><a href="/collections/network-streamers" class="text-sm hover:text-primary">Streamers</a></li>
                    <li><a href="/collections/cd-players" class="text-sm hover:text-primary">CD Players</a></li>
                  </ul>
                </div>
                <div class="col-span-1">
                  <h3 class="font-medium mb-2">Wireless</h3>
                  <ul class="space-y-1">
                    <li><a href="/collections/true-wireless-earbuds" class="text-sm hover:text-primary">True Wireless Earbuds</a></li>
                    <li><a href="/collections/wireless-bluetooth-headphones" class="text-sm hover:text-primary">Wireless Headphones</a></li>
                    <li><a href="/collections/bluetooth-wireless-earphones" class="text-sm hover:text-primary">Wireless Neckband Earphones</a></li>
                    <li><a href="/collections/noise-cancelling-headphones" class="text-sm hover:text-primary">Noise Cancelling Headphones</a></li>
                    <li><a href="/collections/for-sports-and-gym" class="text-sm hover:text-primary">For Sports and Gym</a></li>
                  </ul>
                </div>
                <div class="col-span-1">
                  <h3 class="font-medium mb-2">Cables</h3>
                  <ul class="space-y-1">
                    <li><a href="/collections/iem-cables" class="text-sm hover:text-primary">IEM Cables</a></li>
                    <li><a href="/collections/headphone-cables" class="text-sm hover:text-primary">Headphone Cables</a></li>
                    <li><a href="/collections/adapters" class="text-sm hover:text-primary">Adapters</a></li>
                    <li><a href="/collections/digital-cables" class="text-sm hover:text-primary">Digital Interconnect</a></li>
                    <li><a href="/collections/analog-cables" class="text-sm hover:text-primary">Analog Interconnect</a></li>
                  </ul>
                </div>
                <div class="col-span-1">
                  <h3 class="font-medium mb-2">Accessories</h3>
                  <ul class="space-y-1">
                    <li><a href="/collections/eartips" class="text-sm hover:text-primary">Eartips</a></li>
                    <li><a href="/collections/earpads" class="text-sm hover:text-primary">Earpads</a></li>
                    <li><a href="/collections/audiophile-cases" class="text-sm hover:text-primary">Audiophile Cases</a></li>
                    <li><a href="/collections/bluetooth-adapters" class="text-sm hover:text-primary">Bluetooth Adapters</a></li>
                    <li><a href="/collections/headphone-stands" class="text-sm hover:text-primary">Headphone Stands</a></li>
                    <li><a href="/collections/power-accessories" class="text-sm hover:text-primary">Power Accessories</a></li>
                    <li><a href="/collections/mechanical-keyboards" class="text-sm hover:text-primary">Mechanical Keyboards</a></li>
                    <li><a href="/collections/other-accessories" class="text-sm hover:text-primary">Others</a></li>
                  </ul>
                </div>
                <div class="col-span-1">
                  <h3 class="font-medium mb-2">Home Audio</h3>
                  <ul class="space-y-1">
                    <li><a href="/collections/speakers" class="text-sm hover:text-primary">Speakers</a></li>
                    <li><a href="/collections/speaker-amps" class="text-sm hover:text-primary">Speaker Amps</a></li>
                    <li><a href="/collections/speaker-stands" class="text-sm hover:text-primary">Speaker Stands</a></li>
                    <li><a href="/collections/network-streamers" class="text-sm hover:text-primary">Streamers</a></li>
                    <li><a href="/collections/cd-players" class="text-sm hover:text-primary">CD Players</a></li>
                    <li><a href="/collections/turntables" class="text-sm hover:text-primary">Turntables</a></li>
                  </ul>
                </div>
              </div>
            </div>
          </div>

          <!-- Mega Menu -->
          <div *ngIf="category.subcategories && category.subcategories.length > 0 && activeCategory === category"
               [@megaMenuAnimation]
               class="fixed left-0 right-0 top-[64px] bg-white shadow-lg rounded-b-md p-6 z-50 mega-menu">

            <!-- Categories Mega Menu -->
            <div *ngIf="category.name === 'Categories'" class="grid grid-cols-8 gap-6">
              <div class="col-span-8 mb-4">
                <h2 class="text-xl font-bold text-gray-800 border-b pb-2">Shop By Category</h2>
              </div>

              <div *ngFor="let subcategory of category.subcategories" class="col-span-1">
                <h3 class="font-medium text-gray-800 mb-3">{{ subcategory.name }}</h3>
                <ul class="space-y-2">
                  <li *ngFor="let item of subcategory.items">
                    <a [routerLink]="item.path"
                      class="flex items-center space-x-2 hover:text-primary transition-colors text-xs menu-link">
                      <span>{{ item.name }}</span>
                    </a>
                  </li>
                </ul>
              </div>
            </div>

            <!-- Brands Mega Menu -->
            <div *ngIf="category.name === 'Brands'" class="grid grid-cols-4 gap-6">
              <div *ngFor="let subcategory of category.subcategories" class="space-y-4">
                <h3 class="font-medium text-gray-800 border-b pb-2">{{ subcategory.name }}</h3>
                <ul class="space-y-2">
                  <li *ngFor="let item of subcategory.items">
                    <a [routerLink]="item.path"
                      class="flex items-center space-x-2 hover:text-primary transition-colors text-sm menu-link">
                      <span>{{ item.name }}</span>
                    </a>
                  </li>
                </ul>
              </div>
            </div>

            <!-- Other Mega Menus -->
            <div *ngIf="category.name !== 'Categories' && category.name !== 'Brands'" class="grid grid-cols-4 gap-6">
              <div *ngFor="let subcategory of category.subcategories" class="space-y-4">
                <h3 class="font-medium text-gray-800 border-b pb-2">{{ subcategory.name }}</h3>
                <ul class="space-y-3">
                  <li *ngFor="let item of subcategory.items">
                    <a [routerLink]="item.path"
                      class="flex items-center space-x-2 hover:text-primary transition-colors text-sm menu-link">
                      <div *ngIf="item.image" class="w-10 h-10 flex-shrink-0 overflow-hidden rounded">
                        <img [src]="item.image" [alt]="item.name" class="w-full h-full object-cover rounded transition-transform duration-300 hover:scale-110">
                      </div>
                      <span>{{ item.name }}</span>
                    </a>
                  </li>
                </ul>
              </div>

              <!-- Featured Images -->
              <div *ngIf="category.featured" class="col-span-1">
                <a [routerLink]="category.featured.path" class="block">
                  <div class="overflow-hidden rounded">
                    <img [src]="category.featured.image" [alt]="category.featured.name"
                      class="w-full h-auto rounded transition-transform duration-500 hover:scale-105">
                  </div>
                  <p class="mt-2 text-sm font-medium text-center">{{ category.featured.name }}</p>
                </a>
              </div>
            </div>
          </div>
        </div>
      </nav>

      <!-- Search Bar -->
      <div class="flex-1 max-w-md mx-6 hidden lg:flex">
        <form class="w-full relative" (ngSubmit)="onSearch()">
          <div class="search-container border border-gray-300 rounded-md flex items-center px-3 py-2">
            <span class="text-gray-400 mr-2">
              <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
                <circle cx="11" cy="11" r="8"></circle>
                <line x1="21" y1="21" x2="16.65" y2="16.65"></line>
              </svg>
            </span>
            <input [formControl]="searchControl" type="search" placeholder="Search headphones, earbuds..."
              class="w-full border-none outline-none bg-transparent text-sm" [matAutocomplete]="auto">
          </div>

          <mat-autocomplete #auto="matAutocomplete">
            <mat-option *ngFor="let option of filteredOptions$ | async" [value]="option">
              {{ option }}
            </mat-option>
            <mat-option *ngIf="hasSearchError" disabled class="text-error">
              Error loading suggestions
            </mat-option>
          </mat-autocomplete>
        </form>
      </div>

      <!-- Actions -->
      <div class="flex items-center space-x-4">
        <!-- Mobile Search Toggle -->
        <button mat-icon-button class="lg:hidden" (click)="toggleSearch()"
          [attr.aria-label]="isSearchExpanded ? 'Close search' : 'Open search'">
          <mat-icon>{{ isSearchExpanded ? 'close' : 'search' }}</mat-icon>
        </button>

        <!-- Account -->
        <button mat-icon-button [matMenuTriggerFor]="accountMenu" aria-label="Account menu" class="hidden md:flex">
          <mat-icon>account_circle</mat-icon>
        </button>
        <mat-menu #accountMenu="matMenu">
          <button mat-menu-item>
            <mat-icon>person</mat-icon>
            <span>Login</span>
          </button>
          <button mat-menu-item>
            <mat-icon>favorite</mat-icon>
            <span>Wishlist</span>
          </button>
          <button mat-menu-item>
            <mat-icon>receipt</mat-icon>
            <span>Orders</span>
          </button>
        </mat-menu>

        <!-- Cart -->
        <div class="shopping-cart-container">
          <button (click)="toggleCart()" class="relative flex items-center"
            [attr.aria-label]="'Shopping cart with ' + (cartCount$ | async) + ' items'">
            <span class="material-icons mr-1">shopping_cart</span>
            <span class="cart-count bg-blue-600 text-white rounded-full w-5 h-5 flex items-center justify-center text-xs absolute -top-1 -right-1">
              {{ cartCount$ | async }}
            </span>
          </button>
        </div>

        <!-- Mobile Menu -->
        <button mat-icon-button class="lg:hidden" (click)="toggleMobileMenu()" aria-label="Open menu">
          <mat-icon>menu</mat-icon>
        </button>
      </div>
    </div>
  </div>

  <!-- Mobile Search Bar (Expandable) -->
  <div class="lg:hidden bg-white border-t border-gray-200 overflow-hidden transition-all duration-300"
    [ngClass]="{'h-16 py-2': isSearchExpanded, 'h-0 py-0': !isSearchExpanded}">
    <div class="container mx-auto px-4">
      <form class="w-full relative" (ngSubmit)="onSearch()">
        <mat-form-field appearance="outline" class="w-full">
          <mat-icon matPrefix class="text-gray-400">search</mat-icon>
          <input matInput [formControl]="searchControl" type="search" placeholder="Search headphones, earphones..."
            [matAutocomplete]="mobileAuto">
          <button *ngIf="searchControl.value" type="button" matSuffix mat-icon-button (click)="clearSearch()">
            <mat-icon>close</mat-icon>
          </button>
        </mat-form-field>

        <mat-autocomplete #mobileAuto="matAutocomplete">
          <mat-option *ngFor="let option of filteredOptions$ | async" [value]="option">
            {{ option }}
          </mat-option>
        </mat-autocomplete>
      </form>
    </div>
  </div>

  <!-- Mobile Menu Drawer -->
  <div class="fixed inset-0 bg-black bg-opacity-50 transition-opacity z-50" [class.opacity-100]="isMobileMenuOpen"
    [class.pointer-events-auto]="isMobileMenuOpen" [class.opacity-0]="!isMobileMenuOpen"
    [class.pointer-events-none]="!isMobileMenuOpen" (click)="toggleMobileMenu()">
    <div class="fixed top-0 left-0 bottom-0 w-full max-w-xs bg-white shadow-xl transform transition-transform"
      [class.translate-x-0]="isMobileMenuOpen" [class.-translate-x-full]="!isMobileMenuOpen"
      (click)="$event.stopPropagation()">
      <!-- Mobile Menu Header -->
      <div class="p-4 border-b flex items-center justify-between">
        <h2 class="text-lg font-semibold">Menu</h2>
        <button mat-icon-button (click)="toggleMobileMenu()">
          <mat-icon>close</mat-icon>
        </button>
      </div>

      <!-- Mobile Menu Items -->
      <div class="overflow-y-auto h-[calc(100vh-64px)]">
        <mat-accordion multi="true">
          <ng-container *ngFor="let category of categories">
            <!-- Categories with subcategories -->
            <mat-expansion-panel *ngIf="category.subcategories" class="shadow-none border-b">
              <mat-expansion-panel-header>
                <mat-panel-title>{{ category.name }}</mat-panel-title>
              </mat-expansion-panel-header>

              <!-- Special handling for Categories section -->
              <ng-container *ngIf="category.name === 'Categories'">
                <mat-accordion class="nested-accordion">
                  <mat-expansion-panel *ngFor="let subcategory of category.subcategories" class="shadow-none border-b">
                    <mat-expansion-panel-header>
                      <mat-panel-title class="text-sm">{{ subcategory.name }}</mat-panel-title>
                    </mat-expansion-panel-header>
                    <mat-nav-list dense>
                      <a mat-list-item *ngFor="let item of subcategory.items" [routerLink]="item.path"
                        (click)="toggleMobileMenu()">
                        <span class="text-xs">{{ item.name }}</span>
                      </a>
                    </mat-nav-list>
                  </mat-expansion-panel>
                </mat-accordion>
              </ng-container>

              <!-- Regular subcategories -->
              <ng-container *ngIf="category.name !== 'Categories'">
                <ng-container *ngFor="let subcategory of category.subcategories">
                  <h3 class="font-medium text-sm mt-3 mb-2 text-gray-700">{{ subcategory.name }}</h3>
                  <mat-nav-list dense>
                    <a mat-list-item *ngFor="let item of subcategory.items" [routerLink]="item.path"
                      (click)="toggleMobileMenu()">
                      <span class="text-sm">{{ item.name }}</span>
                    </a>
                  </mat-nav-list>
                </ng-container>
              </ng-container>

              <!-- Featured item for mobile -->
              <div *ngIf="category.featured" class="mt-4 border-t pt-4">
                <h3 class="font-medium text-sm mb-2 text-gray-700">Featured</h3>
                <a [routerLink]="category.featured.path" (click)="toggleMobileMenu()" class="block">
                  <div class="rounded overflow-hidden mb-2">
                    <img [src]="category.featured.image" [alt]="category.featured.name" class="w-full h-auto">
                  </div>
                  <p class="text-sm font-medium">{{ category.featured.name }}</p>
                </a>
              </div>
            </mat-expansion-panel>

            <!-- Direct links -->
            <a *ngIf="!category.subcategories" mat-list-item [href]="category.path" target="_blank" (click)="toggleMobileMenu()"
              class="border-b py-3 px-4 block hover:bg-gray-50">
              {{ category.name }}
              <mat-icon class="ml-2 text-sm">open_in_new</mat-icon>
            </a>
          </ng-container>
        </mat-accordion>

        <!-- Additional Mobile Links -->
        <div class="p-4 border-t">
          <h3 class="text-xs font-medium text-gray-500 mb-3">ACCOUNT</h3>
          <mat-nav-list dense>
            <a mat-list-item routerLink="/account/login" (click)="toggleMobileMenu()">
              <mat-icon matListItemIcon>person</mat-icon>
              <span matListItemTitle>Login</span>
            </a>
            <a mat-list-item routerLink="/account/orders" (click)="toggleMobileMenu()">
              <mat-icon matListItemIcon>receipt</mat-icon>
              <span matListItemTitle>Orders</span>
            </a>
            <a mat-list-item routerLink="/account/wishlist" (click)="toggleMobileMenu()">
              <mat-icon matListItemIcon>favorite</mat-icon>
              <span matListItemTitle>Wishlist</span>
            </a>
          </mat-nav-list>
        </div>

        <!-- Contact Info -->
        <div class="p-4 border-t bg-gray-50">
          <h3 class="text-xs font-medium text-gray-500 mb-3">CONTACT US</h3>
          <div class="space-y-2">
            <a href="tel:+************" class="flex items-center text-sm text-gray-700">
              <mat-icon class="text-sm mr-2">phone</mat-icon>
              <span>+91 98765 43210</span>
            </a>
            <a href="mailto:<EMAIL>" class="flex items-center text-sm text-gray-700">
              <mat-icon class="text-sm mr-2">email</mat-icon>
              <span>support&#64;headphonezone.in</span>
            </a>
          </div>
        </div>
      </div>
    </div>
  </div>

  <!-- Cart Drawer -->
  <div class="fixed inset-0 bg-black bg-opacity-50 transition-opacity z-50" [class.opacity-100]="isCartOpen"
    [class.pointer-events-auto]="isCartOpen" [class.opacity-0]="!isCartOpen" [class.pointer-events-none]="!isCartOpen"
    (click)="toggleCart()">
    <div class="fixed top-0 right-0 bottom-0 w-full max-w-md bg-white shadow-xl transform transition-transform"
      [class.translate-x-0]="isCartOpen" [class.translate-x-full]="!isCartOpen" (click)="$event.stopPropagation()">
      <!-- Cart Header -->
      <div class="p-4 border-b flex items-center justify-between">
        <h2 class="text-lg font-semibold">Your Cart</h2>
        <button mat-icon-button (click)="toggleCart()">
          <mat-icon>close</mat-icon>
        </button>
      </div>

      <!-- Cart Items -->
      <div class="overflow-y-auto h-[calc(100vh-180px)] p-4">
        <ng-container *ngIf="cartItemCount$ | async as itemCount">
          <div *ngIf="itemCount === 0" class="text-center py-8">
            <mat-icon class="text-gray-400 text-5xl mb-4">shopping_cart</mat-icon>
            <p class="text-gray-600">Your cart is empty</p>
            <button mat-stroked-button color="primary" class="mt-4" routerLink="/products" (click)="toggleCart()">
              Start Shopping
            </button>
          </div>
        </ng-container>
      </div>

      <!-- Cart Footer -->
      <div class="absolute bottom-0 left-0 right-0 p-4 border-t bg-white">
        <div class="flex justify-between mb-4">
          <span class="font-semibold">Total:</span>
          <span class="font-bold text-xl" [@cartTotal]>
            ₹{{ cartTotal$ | async | number:'1.2-2' }}
          </span>
        </div>
        <button mat-raised-button color="primary" class="w-full btn-primary" [disabled]="!(cartCount$ | async)"
          routerLink="/checkout" (click)="toggleCart()">
          CHECKOUT
        </button>
        <p class="text-xs text-center mt-3 text-gray-500">Shipping calculated at checkout</p>
      </div>
    </div>
  </div>
</header>

<!-- Spacer for fixed header -->
<div class="h-20"></div>