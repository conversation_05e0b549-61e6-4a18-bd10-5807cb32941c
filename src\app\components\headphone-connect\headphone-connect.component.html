<section class="headphone-connect-section py-12">
  <div class="container mx-auto px-4">
    <div class="text-center mb-10">
      <h2 class="text-3xl font-bold mb-3">Headphone Connect Events</h2>
      <p class="text-gray-600 max-w-2xl mx-auto">Join fellow audiophiles for an evening of music, craft beer, and the finest headphones. Experience high-end audio gear in a relaxed setting.</p>
    </div>

    <!-- Events Grid -->
    <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8">
      <div *ngFor="let event of upcomingEvents" class="event-card bg-white rounded-lg overflow-hidden shadow-md hover:shadow-lg transition-shadow">
        <div class="relative">
          <img [src]="event.imageUrl" [alt]="event.title" class="w-full h-48 object-cover">
          <div class="absolute top-0 right-0 bg-primary text-white px-3 py-1 m-2 rounded text-sm font-medium">
            {{ event.city }}
          </div>
        </div>

        <div class="p-6">
          <h3 class="text-xl font-semibold mb-2">{{ event.title }}</h3>

          <div class="flex items-center mb-3 text-gray-600">
            <mat-icon class="text-primary mr-2">calendar_today</mat-icon>
            <span>{{ formatDate(event.date) }}</span>
          </div>

          <div class="flex items-center mb-3 text-gray-600">
            <mat-icon class="text-primary mr-2">schedule</mat-icon>
            <span>{{ formatTime(event.date) }}</span>
          </div>

          <div class="flex items-center mb-4 text-gray-600">
            <mat-icon class="text-primary mr-2">location_on</mat-icon>
            <span>{{ event.location }}</span>
          </div>

          <p class="text-gray-700 mb-4">{{ event.description }}</p>

          <div class="flex items-center justify-between">
            <div>
              <span class="text-sm text-gray-500">Tickets available:</span>
              <span class="font-medium ml-1">{{ event.ticketsAvailable }}</span>
            </div>
            <div *ngIf="event.ticketPrice" class="text-primary font-semibold">
              ₹{{ event.ticketPrice }}
            </div>
          </div>

          <button
            mat-raised-button
            color="primary"
            class="w-full mt-4"
            [disabled]="!event.registrationOpen || event.ticketsAvailable === 0"
            (click)="openRegistrationModal(event)">
            {{ event.registrationOpen && event.ticketsAvailable > 0 ? 'REGISTER NOW' : 'SOLD OUT' }}
          </button>
        </div>
      </div>
    </div>

    <!-- Past Events Link -->
    <div class="text-center mt-10">
      <a routerLink="/pages/headphone-connect" class="inline-flex items-center text-primary hover:underline">
        <span>View past events</span>
        <mat-icon class="ml-1">arrow_forward</mat-icon>
      </a>
    </div>
  </div>

  <!-- Registration Modal -->
  <div *ngIf="selectedEvent" class="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50 p-4">
    <div class="bg-white rounded-lg max-w-md w-full max-h-[90vh] overflow-y-auto">
      <div class="p-6">
        <div class="flex justify-between items-center mb-4">
          <h3 class="text-xl font-semibold">Register for {{ selectedEvent.title }}</h3>
          <button mat-icon-button (click)="closeRegistrationModal()">
            <mat-icon>close</mat-icon>
          </button>
        </div>

        <div *ngIf="!registrationSuccess; else successMessage">
          <form [formGroup]="registrationForm" (ngSubmit)="registerForEvent()">
            <mat-form-field appearance="outline" class="w-full mb-4">
              <mat-label>Full Name</mat-label>
              <input matInput formControlName="name" placeholder="Enter your full name">
              <mat-error *ngIf="registrationForm.get('name')?.hasError('required')">
                Name is required
              </mat-error>
            </mat-form-field>

            <mat-form-field appearance="outline" class="w-full mb-4">
              <mat-label>Email</mat-label>
              <input matInput formControlName="email" placeholder="Enter your email" type="email">
              <mat-error *ngIf="registrationForm.get('email')?.hasError('required')">
                Email is required
              </mat-error>
              <mat-error *ngIf="registrationForm.get('email')?.hasError('email')">
                Please enter a valid email
              </mat-error>
            </mat-form-field>

            <mat-form-field appearance="outline" class="w-full mb-4">
              <mat-label>Phone Number</mat-label>
              <input matInput formControlName="phone" placeholder="Enter your phone number">
              <mat-error *ngIf="registrationForm.get('phone')?.hasError('required')">
                Phone number is required
              </mat-error>
              <mat-error *ngIf="registrationForm.get('phone')?.hasError('pattern')">
                Please enter a valid 10-digit phone number
              </mat-error>
            </mat-form-field>

            <mat-form-field appearance="outline" class="w-full mb-4">
              <mat-label>Number of Tickets</mat-label>
              <input matInput formControlName="quantity" type="number" min="1" max="5">
              <mat-hint>Maximum 5 tickets per registration</mat-hint>
              <mat-error *ngIf="registrationForm.get('quantity')?.hasError('required')">
                Quantity is required
              </mat-error>
              <mat-error *ngIf="registrationForm.get('quantity')?.hasError('min')">
                Minimum 1 ticket required
              </mat-error>
              <mat-error *ngIf="registrationForm.get('quantity')?.hasError('max')">
                Maximum 5 tickets allowed
              </mat-error>
            </mat-form-field>

            <div *ngIf="selectedEvent.ticketPrice" class="mb-4 p-4 bg-gray-50 rounded-lg">
              <div class="flex justify-between mb-2">
                <span>Ticket Price:</span>
                <span>₹{{ selectedEvent.ticketPrice }} x {{ registrationForm.get('quantity')?.value || 0 }}</span>
              </div>
              <div class="flex justify-between font-semibold">
                <span>Total:</span>
                <span>₹{{ getTicketTotal() }}</span>
              </div>
            </div>

            <mat-checkbox formControlName="agreeTerms" color="primary" class="mb-4 block">
              I agree to the terms and conditions
              <mat-error *ngIf="registrationForm.get('agreeTerms')?.hasError('required') && registrationForm.get('agreeTerms')?.touched">
                You must agree to the terms and conditions
              </mat-error>
            </mat-checkbox>

            <button mat-raised-button color="primary" type="submit" class="w-full">
              COMPLETE REGISTRATION
            </button>
          </form>
        </div>

        <ng-template #successMessage>
          <div class="text-center py-6">
            <mat-icon class="text-success text-5xl mb-4">check_circle</mat-icon>
            <h3 class="text-xl font-semibold mb-2">Registration Successful!</h3>
            <p class="text-gray-600 mb-4">Thank you for registering for {{ selectedEvent.title }}. We've sent a confirmation email with all the details.</p>
            <button mat-stroked-button color="primary" (click)="closeRegistrationModal()">
              CLOSE
            </button>
          </div>
        </ng-template>
      </div>
    </div>
  </div>
</section>
