<section class="category-showcase py-12 bg-gray-50">
  <div class="container mx-auto px-4">
    <div class="text-center mb-8">
      <h2 class="section-title inline-block">Steals You Can't Miss!</h2>
      <p class="text-gray-600 mt-3 max-w-2xl mx-auto">Discover amazing deals on audiophile gear - from demo units to
        special offers.</p>
    </div>

    <div class="grid grid-cols-1 md:grid-cols-3 gap-6">
      <div *ngFor="let category of categories" class="category-card group">
        <a [routerLink]="category.link" class="block relative">
          <!-- Category Image -->
          <div class="category-image relative overflow-hidden">
            <img [src]="category.image" [alt]="category.name"
              class="w-full h-full object-cover transition-transform duration-700 group-hover:scale-105">
            <div class="absolute inset-0 bg-gradient-to-t from-black/80 via-black/50 to-transparent"></div>
          </div>

          <!-- Category Info -->
          <div class="absolute inset-x 0 bottom 0 p-6 text-white">
            <h3 class="text-2xl font-bold mb-2">{{ category.name }}</h3>
            <p class="text-sm text-gray-200 mb-4 opacity-90">{{ category.description }}</p>
            <div
              class="inline-flex items-center text-sm font-semibold bg-white text-black px-4 py-2 transition-transform duration-300 group-hover:translate-x-2">
              <span>{{ category.buttonText }}</span>
              <span class="ml-2">→</span>
            </div>
          </div>
        </a>
      </div>
    </div>
  </div>
</section>