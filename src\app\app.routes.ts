import { Routes } from '@angular/router';
import { HomeComponent } from './pages/home/<USER>';
import { PaymentSuccessComponent } from './pages/payment-success/payment-success.component';
import { ProductDetailComponent } from './pages/product-detail/product-detail.component';
import { AboutComponent } from './pages/about/about.component';

export const routes: Routes = [
  {
    path: '',
    component: HomeComponent,
  },
  {
    path: 'checkout',
    loadComponent: () =>
      import('./components/checkout/checkout.component').then(
        (m) => m.CheckoutComponent
      ),
  },
  {
    path: 'payment-success',
    component: PaymentSuccessComponent,
  },
  {
    path: 'products',
    loadComponent: () =>
      import('./components/product-grid/product-grid.component').then(
        (m) => m.ProductGridComponent
      ),
  },
  {
    path: 'deals',
    loadComponent: () =>
      import('./components/featured-deals/featured-deals.component').then(
        (m) => m.FeaturedDealsComponent
      ),
  },
  {
    path: 'product/:id',
    component: ProductDetailComponent,
  },
  {
    path: 'collections/:category',
    loadComponent: () =>
      import('./components/product-grid/product-grid.component').then(
        (m) => m.ProductGridComponent
      ),
  },
  {
    path: 'pages/about-us',
    component: AboutComponent,
  },
  {
    path: 'blogs/audiophile-101',
    loadComponent: () =>
      import('./pages/blog/blog-list/blog-list.component').then(
        (m) => m.BlogListComponent
      ),
  },
  {
    path: 'blogs/audiophile-101/:slug',
    loadComponent: () =>
      import('./pages/blog/blog-detail/blog-detail.component').then(
        (m) => m.BlogDetailComponent
      ),
  },
  {
    path: '**',
    redirectTo: '',
  },
];
