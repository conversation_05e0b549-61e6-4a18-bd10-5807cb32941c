import {
  Component,
  OnInit,
  Inject,
  PLATFORM_ID,
  HostListener,
  OnD<PERSON>roy,
} from '@angular/core';
import { CommonModule } from '@angular/common';
import { RouterModule } from '@angular/router';
import { FormsModule, ReactiveFormsModule } from '@angular/forms';
import { MaterialModule } from '../../material/material.module';
import { FormControl } from '@angular/forms';
import {
  Observable,
  debounceTime,
  distinctUntilChanged,
  filter,
  startWith,
  switchMap,
  tap,
  fromEvent,
  Subject,
} from 'rxjs';
import { takeUntil } from 'rxjs/operators';
import { ProductService } from '../../services/product.service';
import { CartService } from '../../services/cart.service';
import { animate, style, transition, trigger } from '@angular/animations';

interface MenuItem {
  name: string;
  path: string;
  image?: string;
}

interface SubCategory {
  name: string;
  items: MenuItem[];
}

interface FeaturedItem {
  name: string;
  path: string;
  image: string;
}

interface Category {
  name: string;
  path?: string;
  subcategories?: SubCategory[];
  featured?: FeaturedItem;
}

@Component({
  selector: 'app-header',
  standalone: true,
  imports: [
    CommonModule,
    RouterModule,
    FormsModule,
    ReactiveFormsModule,
    MaterialModule,
  ],
  templateUrl: './header.component.html',
  styleUrl: './header.component.css',
  host: {
    '[class.scrolled]': 'isScrolled',
  },
  animations: [
    trigger('cartBadge', [
      transition(':increment', [
        style({ transform: 'scale(1.5)', color: 'var(--primary)' }),
        animate('300ms ease-out', style({ transform: 'scale(1)' })),
      ]),
    ]),
    trigger('cartTotal', [
      transition('* => *', [
        style({ opacity: 0, transform: 'translateY(-20px)' }),
        animate(
          '300ms ease-out',
          style({ opacity: 1, transform: 'translateY(0)' })
        ),
      ]),
    ]),
    trigger('megaMenuAnimation', [
      transition(':enter', [
        style({ opacity: 0, transform: 'translateY(-10px)' }),
        animate('200ms ease-out', style({ opacity: 1, transform: 'translateY(0)' }))
      ]),
      transition(':leave', [
        animate('150ms ease-in', style({ opacity: 0, transform: 'translateY(-10px)' }))
      ])
    ]),
    trigger('fadeIn', [
      transition(':enter', [
        style({ opacity: 0 }),
        animate('300ms ease-out', style({ opacity: 1 }))
      ])
    ]),
  ],
})
export class HeaderComponent implements OnInit, OnDestroy {
  searchControl = new FormControl('');
  filteredOptions$!: Observable<string[]>;
  cartItemCount$!: Observable<number>;
  cartCount$: Observable<number>;
  cartTotal$: Observable<number>;
  isSearchExpanded = false;
  isSearching = false;
  hasSearchError = false;
  isScrolled = false;
  isCartOpen = false;
  window: any;
  private destroy$ = new Subject<void>();

  categories: Category[] = [
    {
      name: 'Categories',
      subcategories: [
        {
          name: 'In-Ears',
          items: [
            { name: 'Beginner Audiophile IEMs', path: '/collections/beginner-audiophile-iems', image: 'assets/images/categories/beginner-audiophile-iems.jpg' },
            { name: 'Flagship Audiophile IEMs', path: '/collections/flagship-audiophile-iems', image: 'assets/images/categories/flagship-audiophile-iems.jpg' },
            { name: 'Our Collabs', path: '/collections/our-collabs', image: 'assets/images/categories/our-collabs.jpg' },
            { name: 'Type-C IEMs', path: '/collections/type-c-in-ear-monitors', image: 'assets/images/categories/type-c-iems.jpg' },
            { name: 'On-Stage Monitors', path: '/collections/on-stage-monitors', image: 'assets/images/categories/on-stage-monitors.jpg' },
            { name: 'IEM Cables', path: '/collections/iem-cables', image: 'assets/images/categories/iem-cables.jpg' },
            { name: 'Eartips', path: '/collections/eartips', image: 'assets/images/categories/eartips.jpg' },
            { name: 'IEMs Bundles', path: '/collections/iems-bundles', image: 'assets/images/categories/iems-bundles.jpg' }
          ]
        },
        {
          name: 'Headphones',
          items: [
            { name: 'Beginner Audiophile', path: '/collections/beginner-audiophile-headphones', image: 'assets/images/categories/beginner-audiophile-headphones.jpg' },
            { name: 'Flagship Headphones', path: '/collections/flagship-audiophile-headphones', image: 'assets/images/categories/flagship-headphones.jpg' },
            { name: 'Studio & Professional', path: '/collections/studio-professional-headphones', image: 'assets/images/categories/studio-professional.jpg' },
            { name: 'For Calls', path: '/collections/work-from-home-headphones', image: 'assets/images/categories/for-calls.jpg' },
            { name: 'Gaming', path: '/collections/gaming-headphones', image: 'assets/images/categories/gaming.jpg' },
            { name: 'Headphone Cables', path: '/collections/headphone-cables', image: 'assets/images/categories/headphone-cables.jpg' },
            { name: 'Earpads', path: '/collections/earpads', image: 'assets/images/categories/earpads.jpg' },
            { name: 'Headphones Bundles', path: '/collections/headphones-bundles', image: 'assets/images/categories/headphones-bundles.jpg' }
          ]
        },
        {
          name: 'DACs & Amps',
          items: [
            { name: 'Portable DACs & Amps', path: '/collections/portable-amps-dacs', image: 'assets/images/categories/portable-dacs-amps.jpg' },
            { name: 'Desktop DACs & Amps', path: '/collections/desktop-amps-dacs', image: 'assets/images/categories/desktop-dacs-amps.jpg' },
            { name: 'Speaker Amps', path: '/collections/speaker-amps', image: 'assets/images/categories/speaker-amps.jpg' },
            { name: 'Network Streamers', path: '/collections/network-streamers', image: 'assets/images/categories/network-streamers.jpg' }
          ]
        },
        {
          name: 'Hi-Res Audio Players',
          items: [
            { name: 'Portable Hi-Res Players', path: '/collections/portable-hi-res-digital-audio-player-daps', image: 'assets/images/categories/portable-players.jpg' },
            { name: 'Desktop Hi-Res Players', path: '/collections/desktop-hi-res-digital-audio-player-daps', image: 'assets/images/categories/desktop-players.jpg' },
            { name: 'Streamers', path: '/collections/network-streamers', image: 'assets/images/categories/streamers.jpg' },
            { name: 'CD Players', path: '/collections/cd-players', image: 'assets/images/categories/cd-players.jpg' }
          ]
        },
        {
          name: 'Wireless',
          items: [
            { name: 'True Wireless Earbuds', path: '/collections/true-wireless-earbuds', image: 'assets/images/categories/true-wireless.jpg' },
            { name: 'Wireless Headphones', path: '/collections/wireless-bluetooth-headphones', image: 'assets/images/categories/wireless-headphones.jpg' },
            { name: 'Wireless Neckband Earphones', path: '/collections/bluetooth-wireless-earphones', image: 'assets/images/categories/neckband.jpg' },
            { name: 'Noise Cancelling Headphones', path: '/collections/noise-cancelling-headphones', image: 'assets/images/categories/noise-cancelling.jpg' },
            { name: 'For Sports and Gym', path: '/collections/for-sports-and-gym', image: 'assets/images/categories/sports.jpg' }
          ]
        },
        {
          name: 'Cables',
          items: [
            { name: 'IEM Cables', path: '/collections/iem-cables', image: 'assets/images/categories/iem-cables.jpg' },
            { name: 'Headphone Cables', path: '/collections/headphone-cables', image: 'assets/images/categories/headphone-cables.jpg' },
            { name: 'Adapters', path: '/collections/adapters', image: 'assets/images/categories/adapters.jpg' },
            { name: 'Digital Interconnect', path: '/collections/digital-cables', image: 'assets/images/categories/digital-cables.jpg' },
            { name: 'Analog Interconnect', path: '/collections/analog-cables', image: 'assets/images/categories/analog-cables.jpg' }
          ]
        },
        {
          name: 'Accessories',
          items: [
            { name: 'Eartips', path: '/collections/eartips', image: 'assets/images/categories/eartips.jpg' },
            { name: 'Earpads', path: '/collections/earpads', image: 'assets/images/categories/earpads.jpg' },
            { name: 'Audiophile Cases', path: '/collections/audiophile-cases', image: 'assets/images/categories/cases.jpg' },
            { name: 'Bluetooth Adapters', path: '/collections/bluetooth-adapters', image: 'assets/images/categories/bluetooth-adapters.jpg' },
            { name: 'Headphone Stands', path: '/collections/headphone-stands', image: 'assets/images/categories/headphone-stands.jpg' },
            { name: 'Power Accessories', path: '/collections/power-accessories', image: 'assets/images/categories/power-accessories.jpg' },
            { name: 'Mechanical Keyboards', path: '/collections/mechanical-keyboards', image: 'assets/images/categories/keyboards.jpg' },
            { name: 'Others', path: '/collections/other-accessories', image: 'assets/images/categories/other-accessories.jpg' }
          ]
        },
        {
          name: 'Home Audio',
          items: [
            { name: 'Speakers', path: '/collections/speakers', image: 'assets/images/categories/speakers.jpg' },
            { name: 'Speaker Amps', path: '/collections/speaker-amps', image: 'assets/images/categories/speaker-amps.jpg' },
            { name: 'Speaker Stands', path: '/collections/speaker-stands', image: 'assets/images/categories/speaker-stands.jpg' },
            { name: 'Streamers', path: '/collections/network-streamers', image: 'assets/images/categories/streamers.jpg' },
            { name: 'CD Players', path: '/collections/cd-players', image: 'assets/images/categories/cd-players.jpg' },
            { name: 'Turntables', path: '/collections/turntables', image: 'assets/images/categories/turntables.jpg' }
          ]
        }
      ],
      featured: {
        name: 'Featured Product',
        path: '/products/headphone-zone-x-oriveti-blackbird',
        image: 'assets/images/featured/blackbird.jpg'
      }
    },
    {
      name: 'Brands',
      subcategories: [
        {
          name: 'Popular Brands',
          items: [
            { name: 'FiiO', path: '/collections/fiio' },
            { name: 'Sennheiser', path: '/collections/sennheiser' },
            { name: 'Sony', path: '/collections/sony-headphones' },
            { name: 'Moondrop', path: '/collections/moondrop' },
            { name: 'KZ Acoustics', path: '/collections/kz-acoustics' },
            { name: 'Meze Audio', path: '/collections/meze-audio' },
            { name: 'iFi Audio', path: '/collections/ifi-audio' },
            { name: 'Focal', path: '/collections/focal' }
          ]
        },
        {
          name: 'Audiophile Brands',
          items: [
            { name: 'Audeze', path: '/collections/audeze' },
            { name: 'Campfire Audio', path: '/collections/campfire-audio' },
            { name: 'HiFiMAN', path: '/collections/hifiman' },
            { name: 'Chord Electronics', path: '/collections/chord-electronics-ltd' },
            { name: 'Dan Clark Audio', path: '/collections/dan-clark-audio' },
            { name: 'Grado', path: '/collections/grado' },
            { name: 'Shanling', path: '/collections/shanling' },
            { name: 'Topping', path: '/collections/topping' }
          ]
        },
        {
          name: 'IEM Brands',
          items: [
            { name: '7HZ', path: '/collections/7hz' },
            { name: 'BLON', path: '/collections/blon' },
            { name: 'CCA', path: '/collections/cca' },
            { name: 'Kinera', path: '/collections/kinera' },
            { name: 'Kiwi Ears', path: '/collections/kiwi-ears' },
            { name: 'Letshuoer', path: '/collections/letshuoer' },
            { name: 'Tangzu', path: '/collections/tangzu' },
            { name: 'ThieAudio', path: '/collections/thieaudio' }
          ]
        },
        {
          name: 'Accessories Brands',
          items: [
            { name: 'ddHiFi', path: '/collections/ddhifi' },
            { name: 'Dekoni Audio', path: '/collections/dekoni-audio' },
            { name: 'SpinFit', path: '/collections/spinfit' },
            { name: 'Comply', path: '/collections/comply' },
            { name: 'Tripowin', path: '/collections/tripowin' },
            { name: 'Effect Audio', path: '/collections/effect-audio' },
            { name: 'Headphone Zone', path: '/collections/headphone-zone-accessories' }
          ]
        }
      ]
    },
    {
      name: 'On Sale',
      subcategories: [
        {
          name: 'Deals',
          items: [
            { name: 'Deals of the Month', path: '/pages/deals-of-the-month', image: 'assets/images/deals/deals-of-month.jpg' },
            { name: 'Unboxed', path: '/collections/unboxed', image: 'assets/images/deals/unboxed.jpg' },
            { name: 'Demo Units', path: '/collections/demo-and-refurbished', image: 'assets/images/deals/demo-units.jpg' },
            { name: 'Clearance', path: '/collections/clearance', image: 'assets/images/deals/clearance.jpg' }
          ]
        },
        {
          name: 'Special Offers',
          items: [
            { name: 'Bundle Deals', path: '/collections/bundle-deals', image: 'assets/images/deals/bundle-deals.jpg' },
            { name: 'Free Gifts', path: '/collections/free-gifts', image: 'assets/images/deals/free-gifts.jpg' },
            { name: 'Limited Time Offers', path: '/collections/limited-time-offers', image: 'assets/images/deals/limited-time.jpg' }
          ]
        }
      ],
      featured: {
        name: 'Deals of the Month',
        path: '/pages/deals-of-the-month',
        image: 'assets/images/deals/featured-deal.jpg'
      }
    },
    {
      name: 'Collections',
      subcategories: [
        {
          name: 'Our Collaborations',
          items: [
            { name: 'JD1', path: '/products/headphone-zone-x-fiio-jd1', image: 'assets/images/favourites/jd1.jpg' },
            { name: 'Wan\'er S.G', path: '/products/headphone-zone-x-tangzu-waner-s-g', image: 'assets/images/favourites/waner.jpg' },
            { name: 'OD200', path: '/products/headphone-zone-x-oriveti-od200', image: 'assets/images/favourites/od200.jpg' },
            { name: 'Blackbird', path: '/products/headphone-zone-x-oriveti-blackbird', image: 'assets/images/favourites/blackbird.jpg' },
            { name: 'View All Collabs', path: '/collections/our-collabs', image: 'assets/images/favourites/all-collabs.jpg' }
          ]
        },
        {
          name: 'Best Sellers',
          items: [
            { name: 'Best Sellers', path: '/collections/best-sellers', image: 'assets/images/favourites/best-sellers.jpg' },
            { name: 'New Arrivals', path: '/collections/new-arrivals', image: 'assets/images/favourites/new-arrivals.jpg' },
            { name: 'Staff Picks', path: '/collections/staff-picks', image: 'assets/images/favourites/staff-picks.jpg' },
            { name: 'Award Winners', path: '/collections/award-winners', image: 'assets/images/favourites/award-winners.jpg' }
          ]
        },
        {
          name: 'By Budget',
          items: [
            { name: 'Best Under ₹ 1,000', path: '/collections/best-headphones-under-rs-1000', image: 'assets/images/favourites/under-1000.jpg' },
            { name: 'Best Under ₹ 2,000', path: '/collections/best-headphones-under-rs-2000', image: 'assets/images/favourites/under-2000.jpg' },
            { name: 'Best Under ₹ 3,000', path: '/collections/best-headphones-under-rs-3000', image: 'assets/images/favourites/under-3000.jpg' },
            { name: 'Best Under ₹ 5,000', path: '/collections/best-headphones-under-rs-5000', image: 'assets/images/favourites/under-5000.jpg' },
            { name: 'Best Under ₹ 10,000', path: '/collections/best-headphones-under-rs-10000', image: 'assets/images/favourites/under-10000.jpg' },
            { name: 'Best Under ₹ 20,000', path: '/collections/best-headphones-under-rs-20000', image: 'assets/images/favourites/under-20000.jpg' },
            { name: 'Best Under ₹ 30,000', path: '/collections/best-headphones-under-rs-30000', image: 'assets/images/favourites/under-30000.jpg' },
            { name: 'Premium (₹30,000+)', path: '/collections/premium-headphones', image: 'assets/images/favourites/premium.jpg' }
          ]
        }
      ],
      featured: {
        name: 'Our Collaborations',
        path: '/collections/our-collabs',
        image: 'assets/images/favourites/featured-collab.jpg'
      }
    },
    {
      name: 'Experiences',
      subcategories: [
        {
          name: 'Headphone Connect',
          items: [
            { name: 'About Headphone Connect', path: '/pages/headphone-connect', image: 'assets/images/experiences/about-hc.jpg' },
            { name: 'Upcoming Events', path: '/pages/upcoming-events', image: 'assets/images/experiences/upcoming-events.jpg' },
            { name: 'Past Events', path: '/pages/past-events', image: 'assets/images/experiences/past-events.jpg' },
            { name: 'Event Gallery', path: '/pages/event-gallery', image: 'assets/images/experiences/event-gallery.jpg' }
          ]
        },
        {
          name: 'Upcoming Events',
          items: [
            { name: 'Headphone Connect Hyderabad', path: '/products/headphone-connect-hyderabad', image: 'assets/images/experiences/hyderabad.jpg' },
            { name: 'Final Audio Make 4 DIY IEM Workshop - Hyderabad', path: '/products/final-audio-make-4-diy-iem-workshop-hyderabad', image: 'assets/images/experiences/final-audio.jpg' },
            { name: 'Headphone Connect Ahmedabad', path: '/products/headphone-connect-ahmedabad', image: 'assets/images/experiences/ahmedabad.jpg' },
            { name: 'Final Audio Make 4 DIY IEM Workshop - Ahmedabad', path: '/products/final-audio-make-4-diy-iem-workshop-ahmedabad', image: 'assets/images/experiences/final-audio-ahmedabad.jpg' }
          ]
        }
      ],
      featured: {
        name: 'Headphone Connect 2025',
        path: '/pages/headphone-connect',
        image: 'assets/images/experiences/headphone-connect.jpg'
      }
    },
    {
      name: 'Learn',
      subcategories: [
        {
          name: 'Audiophile 101',
          items: [
            { name: 'Beginner\'s Guide', path: '/blogs/audiophile-101/beginners-guide', image: 'assets/images/learn/beginners-guide.jpg' },
            { name: 'Buying Guides', path: '/blogs/audiophile-101/buying-guides', image: 'assets/images/learn/buying-guides.jpg' },
            { name: 'Product Reviews', path: '/blogs/audiophile-101/product-reviews', image: 'assets/images/learn/product-reviews.jpg' },
            { name: 'Tech Explained', path: '/blogs/audiophile-101/tech-explained', image: 'assets/images/learn/tech-explained.jpg' },
            { name: 'Interviews', path: '/blogs/audiophile-101/interviews', image: 'assets/images/learn/interviews.jpg' }
          ]
        },
        {
          name: 'Resources',
          items: [
            { name: 'Glossary', path: '/pages/glossary', image: 'assets/images/learn/glossary.jpg' },
            { name: 'FAQ', path: '/pages/faq', image: 'assets/images/learn/faq.jpg' },
            { name: 'Headphone Finder', path: '/pages/headphone-finder', image: 'assets/images/learn/headphone-finder.jpg' },
            { name: 'Sound Signature Guide', path: '/pages/sound-signature-guide', image: 'assets/images/learn/sound-signature.jpg' }
          ]
        }
      ],
      featured: {
        name: 'Audiophile 101',
        path: '/blogs/audiophile-101',
        image: 'assets/images/learn/audiophile-101.jpg'
      }
    },
    {
      name: 'Forum',
      path: 'https://www.theindianaudiophileforum.com/home'
    }
  ];

  isMobileMenuOpen = false;
  activeCategory: Category | null = null;

  constructor(
    private productService: ProductService,
    private cartService: CartService,
    @Inject(PLATFORM_ID) private platformId: Object
  ) {
    this.window = typeof window !== 'undefined' ? window : null;
    this.cartCount$ = this.cartService.getCartCount();
    this.cartTotal$ = this.cartService.getCartTotal();
  }

  ngOnInit(): void {
    this.setupSearch();
    this.setupScrollListener();
    this.cartItemCount$ = this.cartService.getCartCount();
  }

  ngOnDestroy(): void {
    this.destroy$.next();
    this.destroy$.complete();
  }

  private setupSearch(): void {
    this.filteredOptions$ = this.searchControl.valueChanges.pipe(
      takeUntil(this.destroy$),
      startWith(''),
      debounceTime(300),
      distinctUntilChanged(),
      filter((value) => !!value),
      switchMap((value) => {
        this.isSearching = true;
        this.hasSearchError = false;
        return this.productService.getSearchSuggestions(value || '').pipe(
          tap({
            next: () => {
              this.isSearching = false;
            },
            error: () => {
              this.isSearching = false;
              this.hasSearchError = true;
            },
          })
        );
      })
    );
  }

  private setupScrollListener(): void {
    if (this.window) {
      fromEvent(this.window, 'scroll')
        .pipe(takeUntil(this.destroy$), debounceTime(10))
        .subscribe(() => {
          this.isScrolled = this.window.scrollY > 20;
        });
    }
  }

  @HostListener('window:resize')
  onResize(): void {
    if (this.window?.innerWidth >= 768 && !this.isSearchExpanded) {
      this.isSearchExpanded = true;
    }
  }

  onSearch(): void {
    const query = this.searchControl.value;
    if (query && query.trim()) {
      this.isSearching = true;
      this.productService.searchProducts(query).subscribe({
        next: () => (this.isSearching = false),
        error: () => {
          this.isSearching = false;
          this.hasSearchError = true;
        },
      });
    }
  }

  toggleSearch(): void {
    this.isSearchExpanded = !this.isSearchExpanded;
    if (!this.isSearchExpanded) {
      this.searchControl.setValue('');
      this.hasSearchError = false;
    }
  }

  clearSearch(): void {
    this.searchControl.setValue('');
    this.hasSearchError = false;
  }

  toggleCart(): void {
    this.isCartOpen = !this.isCartOpen;

    // Close mobile menu if open
    if (this.isCartOpen && this.isMobileMenuOpen) {
      this.isMobileMenuOpen = false;
    }
  }

  toggleMobileMenu(): void {
    this.isMobileMenuOpen = !this.isMobileMenuOpen;

    // Close cart if open
    if (this.isMobileMenuOpen && this.isCartOpen) {
      this.isCartOpen = false;
    }
  }

  onCategoryHover(category: Category): void {
    this.activeCategory = category;
  }

  onCategoryLeave(): void {
    setTimeout(() => {
      this.activeCategory = null;
    }, 200);
  }

  toggleCategory(category: Category, event: Event): void {
    event.preventDefault();
    event.stopPropagation();

    if (this.activeCategory === category) {
      this.activeCategory = null;
    } else {
      this.activeCategory = category;
    }
  }

  isExternalLink(path: string | undefined): boolean {
    return path ? path.startsWith('http') : false;
  }

  formatPrice(price: number): string {
    return price.toFixed(2);
  }
}
