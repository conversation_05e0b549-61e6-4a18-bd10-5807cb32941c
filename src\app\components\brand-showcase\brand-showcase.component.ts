import { Component } from '@angular/core';
import { CommonModule } from '@angular/common';
import { RouterModule } from '@angular/router';
import { MaterialModule } from '../../material/material.module';

interface Brand {
  name: string;
  logo: string;
  link: string;
}

@Component({
  selector: 'app-brand-showcase',
  standalone: true,
  imports: [
    CommonModule,
    RouterModule,
    MaterialModule
  ],
  templateUrl: './brand-showcase.component.html',
  styleUrls: ['./brand-showcase.component.scss']
})
export class BrandShowcaseComponent {
  brands: Brand[] = [
    {
      name: 'FiiO',
      logo: 'https://placehold.co/250x100/FF5500/FFFFFF?text=FiiO',
      link: '/collections/fiio'
    },
    {
      name: '<PERSON><PERSON><PERSON><PERSON>',
      logo: 'https://placehold.co/250x100/333333/FFFFFF?text=Sennheiser',
      link: '/collections/sennheiser'
    },
    {
      name: 'Sony',
      logo: 'https://placehold.co/250x100/000000/FFFFFF?text=SONY',
      link: '/collections/sony-headphones'
    },
    {
      name: 'Moondr<PERSON>',
      logo: 'https://placehold.co/250x100/6A5ACD/FFFFFF?text=Moondrop',
      link: '/collections/moondrop'
    },
    {
      name: 'KZ Acoustics',
      logo: 'https://placehold.co/250x100/4682B4/FFFFFF?text=KZ+Acoustics',
      link: '/collections/kz-acoustics'
    },
    {
      name: 'Meze Audio',
      logo: '//www.headphonezone.in/cdn/shop/files/Meze-Audio-Logo_250x.png?v=1712903400',
      link: '/collections/meze-audio'
    },
    {
      name: 'iFi Audio',
      logo: '//www.headphonezone.in/cdn/shop/files/iFi-Audio-Logo_250x.png?v=1712903400',
      link: '/collections/ifi-audio'
    },
    {
      name: 'Focal',
      logo: '//www.headphonezone.in/cdn/shop/files/Focal-Logo_250x.png?v=1712903400',
      link: '/collections/focal'
    }
  ];
}
