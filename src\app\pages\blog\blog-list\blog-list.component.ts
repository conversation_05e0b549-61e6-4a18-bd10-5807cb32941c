import { Component, OnInit } from '@angular/core';
import { CommonModule } from '@angular/common';
import { RouterModule } from '@angular/router';
import { MaterialModule } from '../../../material/material.module';
import { HeaderComponent } from '../../../components/header/header.component';
import { FooterComponent } from '../../../components/footer/footer.component';
import { BlogService } from '../../../services/blog.service';
import { BlogPost } from '../../../models/blog.model';

@Component({
  selector: 'app-blog-list',
  standalone: true,
  imports: [
    CommonModule,
    RouterModule,
    MaterialModule,
    HeaderComponent,
    FooterComponent
  ],
  templateUrl: './blog-list.component.html',
  styleUrls: ['./blog-list.component.scss']
})
export class BlogListComponent implements OnInit {
  featuredPosts: BlogPost[] = [];
  recentPosts: BlogPost[] = [];
  categories: string[] = [
    'Beginner\'s Guide',
    'Buying Guides',
    'Product Reviews',
    'Tech Explained',
    'Interviews',
    'News & Events'
  ];

  selectedCategory: string = 'All';

  constructor(private blogService: BlogService) {}

  ngOnInit(): void {
    this.blogService.getPosts().subscribe((posts: BlogPost[]) => {
      // Get featured posts (first 3)
      this.featuredPosts = posts.slice(0, 3);

      // Get recent posts (excluding featured)
      this.recentPosts = posts.slice(3);
    });
  }

  filterByCategory(category: string): void {
    this.selectedCategory = category;

    // In a real app, you would filter posts by category
    // For now, we'll just simulate it
    if (category === 'All') {
      this.blogService.getPosts().subscribe((posts: BlogPost[]) => {
        this.recentPosts = posts.slice(3);
      });
    } else {
      this.blogService.getPosts().subscribe((posts: BlogPost[]) => {
        // Simulate filtering by randomly selecting a subset of posts
        const filtered = posts.filter((post: BlogPost) => post.category === category);
        this.recentPosts = filtered.slice(0, 6);
      });
    }
  }
}
