.hero-section {
  background-color: var(--primary);
  position: relative;
  overflow: hidden;
  
  &::before {
    content: '';
    position: absolute;
    top: -50%;
    left: -50%;
    width: 200%;
    height: 200%;
    background: repeating-linear-gradient(
      45deg,
      rgba(255, 255, 255, 0.1),
      rgba(255, 255, 255, 0.1) 10px,
      transparent 10px,
      transparent 20px
    );
    animation: movePattern 20s linear infinite;
  }
}

@keyframes movePattern {
  0% {
    transform: translate(0, 0);
  }
  100% {
    transform: translate(50px, 50px);
  }
}

// Timeline styling
.timeline-item {
  position: relative;
  
  &:not(:last-child)::after {
    content: '';
    position: absolute;
    top: 24px;
    left: 50%;
    transform: translateX(-50%);
    width: 2px;
    height: calc(100% - 24px);
    background-color: var(--primary);
  }
}

// Team member cards
.team-member {
  transition: all 0.3s ease;
  
  &:hover {
    transform: translateY(-10px);
    box-shadow: 0 10px 20px rgba(0, 0, 0, 0.1);
  }
  
  img {
    transition: all 0.5s ease;
  }
  
  &:hover img {
    transform: scale(1.05);
  }
}
