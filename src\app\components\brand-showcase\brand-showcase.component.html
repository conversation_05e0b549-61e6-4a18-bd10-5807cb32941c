<section class="brand-showcase py-12 bg-white">
  <div class="container mx-auto px-4">
    <div class="text-center mb-8">
      <h2 class="section-title inline-block">Our Brands</h2>
      <p class="text-gray-600 mt-3 max-w-2xl mx-auto">We partner with the world's leading audio brands to bring you the best headphones, earphones, and audio equipment.</p>
    </div>
    
    <div class="grid grid-cols-2 sm:grid-cols-3 md:grid-cols-4 gap-4 md:gap-6">
      <a *ngFor="let brand of brands" [routerLink]="brand.link" class="brand-card flex items-center justify-center p-6 border border-gray-200 rounded-lg hover:border-primary transition-colors">
        <img [src]="brand.logo" [alt]="brand.name" class="max-h-12 max-w-full grayscale hover:grayscale-0 transition-all duration-300">
      </a>
    </div>
    
    <div class="text-center mt-10">
      <a routerLink="/collections/all-brands" class="btn-outline inline-flex items-center px-6 py-2 border border-primary text-primary hover:bg-primary hover:text-white transition-colors rounded">
        <span>View All Brands</span>
        <mat-icon class="ml-2">arrow_forward</mat-icon>
      </a>
    </div>
  </div>
</section>
