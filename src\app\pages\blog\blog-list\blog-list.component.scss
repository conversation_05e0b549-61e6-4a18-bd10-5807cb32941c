.blog-container {
  background-color: #f9fafb;
  min-height: calc(100vh - 200px);
}

.page-header {
  text-align: center;
  margin-bottom: 3rem;
}

.page-title {
  font-size: 2.5rem;
  font-weight: 700;
  color: var(--text-primary);
  margin-bottom: 0.5rem;
  
  &::after {
    content: '';
    display: block;
    width: 60px;
    height: 3px;
    background-color: var(--primary);
    margin: 1rem auto 0;
    border-radius: 3px;
  }
}

.page-subtitle {
  color: var(--text-secondary);
  font-size: 1.125rem;
  max-width: 600px;
  margin: 0 auto;
}

// Featured Posts
.featured-posts {
  margin-bottom: 4rem;
}

.featured-post {
  height: 400px;
  
  &.main-feature {
    grid-column: span 2;
    
    @media (max-width: 768px) {
      grid-column: span 1;
    }
    
    .post-title {
      font-size: 1.75rem;
    }
  }
}

.post-card {
  display: block;
  height: 100%;
  border-radius: 8px;
  overflow: hidden;
  box-shadow: 0 4px 6px rgba(0, 0, 0, 0.05), 0 1px 3px rgba(0, 0, 0, 0.1);
  background-color: white;
  transition: transform 0.3s ease, box-shadow 0.3s ease;
  
  &:hover {
    transform: translateY(-5px);
    box-shadow: 0 10px 15px rgba(0, 0, 0, 0.1);
  }
}

.post-image {
  height: 200px;
  position: relative;
  overflow: hidden;
}

.post-category {
  position: absolute;
  top: 1rem;
  left: 1rem;
  background-color: var(--primary);
  color: white;
  padding: 0.25rem 0.75rem;
  border-radius: 20px;
  font-size: 0.75rem;
  font-weight: 500;
}

.post-content {
  padding: 1.5rem;
}

.post-title {
  font-size: 1.25rem;
  font-weight: 600;
  color: var(--text-primary);
  margin-bottom: 0.75rem;
  line-height: 1.4;
}

.post-excerpt {
  color: var(--text-secondary);
  font-size: 0.875rem;
  margin-bottom: 1rem;
  line-height: 1.6;
  display: -webkit-box;
  -webkit-line-clamp: 3;
  -webkit-box-orient: vertical;
  overflow: hidden;
}

.post-meta {
  display: flex;
  justify-content: space-between;
  color: var(--text-secondary);
  font-size: 0.75rem;
}

// Category Filter
.category-filter {
  margin-bottom: 2rem;
}

.filter-container {
  display: flex;
  flex-wrap: wrap;
  gap: 0.5rem;
  justify-content: center;
}

.filter-button {
  padding: 0.5rem 1rem;
  border-radius: 20px;
  background-color: white;
  border: 1px solid #e5e7eb;
  color: var(--text-secondary);
  font-size: 0.875rem;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.2s ease;
  
  &:hover {
    background-color: #f9fafb;
  }
  
  &.active {
    background-color: var(--primary);
    color: white;
    border-color: var(--primary);
  }
}

// Recent Posts
.recent-posts {
  margin-bottom: 4rem;
}

.post-card-container {
  margin-bottom: 2rem;
}

.load-more-container {
  display: flex;
  justify-content: center;
  margin-top: 2rem;
}

.load-more-button {
  min-width: 200px;
  height: 48px;
}

// Newsletter
.blog-newsletter {
  margin-top: 4rem;
  margin-bottom: 2rem;
}

.newsletter-container {
  background-color: white;
  border-radius: 8px;
  box-shadow: 0 4px 6px rgba(0, 0, 0, 0.05), 0 1px 3px rgba(0, 0, 0, 0.1);
  padding: 3rem 2rem;
  text-align: center;
}

.newsletter-title {
  font-size: 1.75rem;
  font-weight: 600;
  color: var(--text-primary);
  margin-bottom: 1rem;
}

.newsletter-description {
  color: var(--text-secondary);
  max-width: 600px;
  margin: 0 auto 2rem;
}

.newsletter-form {
  display: flex;
  max-width: 500px;
  margin: 0 auto;
  
  @media (max-width: 640px) {
    flex-direction: column;
  }
}

.email-field {
  flex: 1;
  margin-right: 1rem;
  
  @media (max-width: 640px) {
    margin-right: 0;
    margin-bottom: 1rem;
  }
}

.subscribe-button {
  height: 56px;
  min-width: 120px;
  
  @media (max-width: 640px) {
    width: 100%;
  }
}
