<app-header></app-header>

<div class="blog-container">
  <div class="container mx-auto px-4 py-8">
    <div class="page-header mb-8">
      <h1 class="page-title">Audiophile 101</h1>
      <p class="page-subtitle">Discover the world of high-fidelity audio</p>
    </div>

    <!-- Featured Posts -->
    <section class="featured-posts mb-12">
      <div class="grid grid-cols-1 md:grid-cols-3 gap-6">
        <div *ngFor="let post of featuredPosts; let i = index" class="featured-post" [class.main-feature]="i === 0">
          <a [routerLink]="['/blogs/audiophile-101', post.slug]" class="post-card">
            <div class="post-image">
              <img [src]="post.imageUrl" [alt]="post.title" class="w-full h-full object-cover">
              <div class="post-category">{{ post.category }}</div>
            </div>
            <div class="post-content">
              <h2 class="post-title">{{ post.title }}</h2>
              <p class="post-excerpt">{{ post.excerpt }}</p>
              <div class="post-meta">
                <span class="post-date">{{ post.publishDate | date:'mediumDate' }}</span>
                <span class="post-author">by {{ post.author }}</span>
              </div>
            </div>
          </a>
        </div>
      </div>
    </section>

    <!-- Category Filter -->
    <section class="category-filter mb-8">
      <div class="filter-container">
        <button 
          class="filter-button" 
          [class.active]="selectedCategory === 'All'"
          (click)="filterByCategory('All')">
          All
        </button>
        <button 
          *ngFor="let category of categories" 
          class="filter-button" 
          [class.active]="selectedCategory === category"
          (click)="filterByCategory(category)">
          {{ category }}
        </button>
      </div>
    </section>

    <!-- Recent Posts -->
    <section class="recent-posts">
      <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
        <div *ngFor="let post of recentPosts" class="post-card-container">
          <a [routerLink]="['/blogs/audiophile-101', post.slug]" class="post-card">
            <div class="post-image">
              <img [src]="post.imageUrl" [alt]="post.title" class="w-full h-full object-cover">
              <div class="post-category">{{ post.category }}</div>
            </div>
            <div class="post-content">
              <h2 class="post-title">{{ post.title }}</h2>
              <p class="post-excerpt">{{ post.excerpt }}</p>
              <div class="post-meta">
                <span class="post-date">{{ post.publishDate | date:'mediumDate' }}</span>
                <span class="post-author">by {{ post.author }}</span>
              </div>
            </div>
          </a>
        </div>
      </div>

      <!-- Load More Button -->
      <div class="load-more-container">
        <button mat-stroked-button color="primary" class="load-more-button">
          Load More Articles
        </button>
      </div>
    </section>

    <!-- Newsletter -->
    <section class="blog-newsletter">
      <div class="newsletter-container">
        <div class="newsletter-content">
          <h2 class="newsletter-title">Stay Updated</h2>
          <p class="newsletter-description">
            Subscribe to our newsletter to receive the latest articles, product reviews, and audiophile news.
          </p>
          <div class="newsletter-form">
            <mat-form-field appearance="outline" class="email-field">
              <mat-label>Email Address</mat-label>
              <input matInput type="email" placeholder="<EMAIL>">
            </mat-form-field>
            <button mat-raised-button color="primary" class="subscribe-button">
              Subscribe
            </button>
          </div>
        </div>
      </div>
    </section>
  </div>
</div>

<app-footer></app-footer>
