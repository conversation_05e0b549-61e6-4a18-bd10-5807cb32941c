:host {
  display: block;
  width: 100%;
}

.banner-slider {
  margin-bottom: 0;
  cursor: none; // Hide default cursor when custom cursor is active

  &:focus {
    outline: none;
  }

  &:focus-visible {
    @apply ring-2 ring-primary ring-offset-2;
  }
}

// Custom cursor with timer
.custom-cursor {
  position: fixed;
  width: 40px;
  height: 40px;
  pointer-events: none; // Allow clicks to pass through
  z-index: 1000;
  opacity: 0;
  transform: translate(-50%, -50%) scale(0.8);
  transition: opacity 0.2s ease, transform 0.2s ease;

  &.active {
    opacity: 1;
    transform: translate(-50%, -50%) scale(1);
  }

  .cursor-timer {
    width: 100%;
    height: 100%;
  }

  .cursor-timer-bg {
    opacity: 0.3;
  }

  .cursor-timer-progress {
    transform: rotate(-90deg);
    transform-origin: center;
    stroke-dasharray: 113; // 2πr where r=18
    transition: stroke-dashoffset 0.1s linear;
  }
}

.banner-container {
  overflow: hidden;
}

.banner-slide {
  &.opacity-100 {
    .banner-image img {
      animation: zoomIn 10s ease forwards;
    }
  }

  transition: opacity 0.5s ease-in-out;
  will-change: opacity, transform;
}

.banner-image {
  img {
    object-fit: cover;
    object-position: center;
    transition: transform 0.8s ease;
  }

  .banner-slide:hover & img {
    transform: scale(1.05);
  }
}

.banner-content {
  opacity: 0;
  transform: translateY(20px);
  transition: all 0.6s ease;
  background-color: rgba(0, 0, 0, 0.3);
  backdrop-filter: blur(5px);
  border-radius: 8px;
  max-width: 500px;

  h2, p, a {
    opacity: 0;
    transform: translateY(20px);
    animation: fadeInUp 0.5s ease forwards;
  }

  h2 {
    animation-delay: 0.2s;
    text-shadow: 0 2px 4px rgba(0, 0, 0, 0.4);
    font-weight: 700;
    line-height: 1.2;
  }

  p {
    animation-delay: 0.4s;
    text-shadow: 0 1px 2px rgba(0, 0, 0, 0.3);
    font-weight: 400;
  }

  a.shop-now-button {
    animation-delay: 0.6s;
    background-color: #ff5500;
    color: white;
    font-weight: 600;
    letter-spacing: 1px;
    padding: 0.75rem 1.5rem;
    border-radius: 0;
    transition: all 0.3s ease;
    text-decoration: none;
    text-transform: uppercase;

    &:hover {
      background-color: darken(#ff5500, 5%);
      transform: translateY(-2px);
    }
  }

  .banner-slide[aria-hidden="false"] & {
    opacity: 1;
    transform: translateY(0);
  }
}

.opacity-100 .banner-content {
  opacity: 1;
  transform: translateY(0);
  transition: all 0.5s ease;
}

.nav-button {
  opacity: 0;
  transform: translateX(10px);
  transition: all 0.3s ease;
  width: 36px;
  height: 36px;
  display: flex;
  align-items: center;
  justify-content: center;

  &:first-child {
    transform: translateX(-10px);
  }

  .banner-slider:hover &,
  .banner-slider:focus-within & {
    opacity: 1;
    transform: translateX(0);
  }

  &:hover {
    transform: scale(1.1);
    background-color: rgba(0, 0, 0, 0.6);
  }

  mat-icon {
    color: white;
    font-size: 24px;
    height: 24px;
    width: 24px;
  }

  @media (max-width: 768px) {
    width: 30px;
    height: 30px;

    mat-icon {
      font-size: 20px;
      height: 20px;
      width: 20px;
    }
  }
}

// Dot indicators
button[role="tab"] {
  transition: all 0.3s ease;
  border: 1px solid rgba(255, 255, 255, 0.7);

  &:hover {
    transform: scale(1.2);
  }

  &[aria-selected="true"] {
    transform: scale(1.1);
    background-color: var(--primary);
    border-color: var(--primary);
  }
}

// Responsive adjustments
@media (max-width: 768px) {
  .banner-content {
    padding: 1rem;
    max-width: 90%;

    h2 {
      font-size: 1.75rem;
      margin-bottom: 0.5rem;
    }

    p {
      font-size: 1rem;
      margin-bottom: 1rem;
    }

    a {
      padding: 0.5rem 1rem;
      font-size: 0.875rem;
    }
  }

  .nav-button {
    opacity: 0.7;
    transform: none;
  }
}

// Tablet adjustments
@media (min-width: 769px) and (max-width: 1024px) {
  .banner-content {
    max-width: 450px;

    h2 {
      font-size: 2.25rem;
    }

    p {
      font-size: 1.125rem;
    }
  }
}

// Reduced motion preferences
@media (prefers-reduced-motion: reduce) {
  .banner-slide,
  .banner-image img,
  .banner-content,
  .nav-button {
    transition: none !important;
    animation: none !important;
  }
}

@keyframes zoomIn {
  0% {
    transform: scale(1);
  }
  100% {
    transform: scale(1.1);
  }
}

@keyframes fadeInUp {
  0% {
    opacity: 0;
    transform: translateY(20px);
  }
  100% {
    opacity: 1;
    transform: translateY(0);
  }
}