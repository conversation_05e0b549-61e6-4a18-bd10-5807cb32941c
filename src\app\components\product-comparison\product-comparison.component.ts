import { Component, OnInit } from '@angular/core';
import { CommonModule } from '@angular/common';
import { RouterModule } from '@angular/router';
import { MaterialModule } from '../../material/material.module';
import { FormsModule, ReactiveFormsModule } from '@angular/forms';
import { ProductService } from '../../services/product.service';
import { CartService } from '../../services/cart.service';
import { ToastService } from '../../services/toast.service';
import { Product } from '../../models/product.model';
import { Observable, BehaviorSubject, combineLatest, map } from 'rxjs';

@Component({
  selector: 'app-product-comparison',
  standalone: true,
  imports: [
    CommonModule,
    RouterModule,
    MaterialModule,
    FormsModule,
    ReactiveFormsModule
  ],
  templateUrl: './product-comparison.component.html',
  styleUrls: ['./product-comparison.component.scss']
})
export class ProductComparisonComponent implements OnInit {
  allProducts$!: Observable<Product[]>;
  selectedProducts$ = new BehaviorSubject<Product[]>([]);
  
  // Comparison categories
  comparisonCategories = [
    {
      name: 'Basic Information',
      properties: [
        { key: 'name', label: 'Name' },
        { key: 'price', label: 'Price', format: 'currency' },
        { key: 'discountedPrice', label: 'Discounted Price', format: 'currency' },
        { key: 'rating', label: 'Rating', format: 'rating' },
        { key: 'category', label: 'Category' }
      ]
    },
    {
      name: 'Technical Specifications',
      properties: [
        { key: 'driverType', label: 'Driver Type' },
        { key: 'driverSize', label: 'Driver Size' },
        { key: 'impedance', label: 'Impedance' },
        { key: 'sensitivity', label: 'Sensitivity' },
        { key: 'frequency', label: 'Frequency Response' },
        { key: 'cableLength', label: 'Cable Length' }
      ]
    },
    {
      name: 'Features',
      properties: [
        { key: 'wireless', label: 'Wireless', format: 'boolean' },
        { key: 'noiseCancelling', label: 'Noise Cancelling', format: 'boolean' },
        { key: 'microphone', label: 'Microphone', format: 'boolean' },
        { key: 'foldable', label: 'Foldable', format: 'boolean' },
        { key: 'waterResistant', label: 'Water Resistant', format: 'boolean' }
      ]
    }
  ];
  
  constructor(
    private productService: ProductService,
    private cartService: CartService,
    private toastService: ToastService
  ) {}
  
  ngOnInit(): void {
    this.allProducts$ = this.productService.getProducts();
    
    // For demo purposes, select the first 3 products
    this.allProducts$.pipe(
      map(products => products.slice(0, 3))
    ).subscribe(products => {
      this.selectedProducts$.next(products);
    });
  }
  
  addProductToCompare(product: Product): void {
    const currentProducts = this.selectedProducts$.value;
    if (currentProducts.length < 4) {
      if (!currentProducts.some(p => p.id === product.id)) {
        this.selectedProducts$.next([...currentProducts, product]);
      } else {
        this.toastService.showError('Product already in comparison');
      }
    } else {
      this.toastService.showError('You can compare up to 4 products');
    }
  }
  
  removeProductFromCompare(productId: number): void {
    const currentProducts = this.selectedProducts$.value;
    this.selectedProducts$.next(currentProducts.filter(p => p.id !== productId));
  }
  
  clearComparison(): void {
    this.selectedProducts$.next([]);
  }
  
  addToCart(product: Product): void {
    this.cartService.addToCart(product);
    this.toastService.showSuccess(`${product.name} added to cart`);
  }
  
  getPropertyValue(product: any, property: any): string {
    if (!product) return 'N/A';
    
    const value = product[property.key];
    if (value === undefined || value === null) return 'N/A';
    
    switch (property.format) {
      case 'currency':
        return `₹${value.toFixed(2)}`;
      case 'rating':
        return `${value}/5`;
      case 'boolean':
        return value ? 'Yes' : 'No';
      default:
        return value.toString();
    }
  }
}
