import { Injectable } from '@angular/core';
import { Observable, of } from 'rxjs';
import { BlogPost } from '../models/blog.model';

@Injectable({
  providedIn: 'root'
})
export class BlogService {
  private posts: BlogPost[] = [
    {
      id: 1,
      title: 'The Ultimate Guide to Headphone Sound Signatures',
      slug: 'ultimate-guide-headphone-sound-signatures',
      excerpt: 'Learn about different sound signatures and find the perfect match for your listening preferences.',
      content: `<p>When it comes to choosing headphones, understanding sound signatures is crucial. A sound signature refers to the way a headphone reproduces sound across different frequencies.</p>
      <h2>What is a Sound Signature?</h2>
      <p>A sound signature is the characteristic sound that a headphone produces based on how it emphasizes or de-emphasizes certain frequency ranges. The main frequency ranges are:</p>
      <ul>
        <li><strong>Bass (20Hz-250Hz)</strong>: Provides the foundation of music, giving it body and impact.</li>
        <li><strong>Midrange (250Hz-2kHz)</strong>: Where most vocals and instruments reside.</li>
        <li><strong>Treble (2kHz-20kHz)</strong>: Adds detail, clarity, and sparkle to the sound.</li>
      </ul>
      <h2>Common Sound Signatures</h2>
      <h3>Neutral/Balanced</h3>
      <p>A neutral sound signature aims to reproduce audio exactly as it was recorded, without emphasizing any particular frequency range. These headphones are ideal for audio professionals and purists who want to hear music as the artist intended.</p>
      <h3>V-Shaped</h3>
      <p>V-shaped sound signatures emphasize both bass and treble while slightly recessing the midrange. This creates an exciting, engaging sound that's popular for modern music genres like EDM, pop, and hip-hop.</p>
      <h3>Warm</h3>
      <p>Warm sound signatures have slightly emphasized bass and lower midrange, with a smoother treble response. This creates a rich, full sound that's easy to listen to for long periods.</p>
      <h3>Bright</h3>
      <p>Bright sound signatures emphasize the upper midrange and treble frequencies, resulting in detailed, clear sound with less bass emphasis. These headphones excel with acoustic music, classical, and vocal performances.</p>`,
      imageUrl: 'https://cdn.shopify.com/s/files/1/0153/8863/files/Headphone-Zone-Sound-Signatures-Banner-2000-x-800_2000x.jpg?v=1651151051',
      category: 'Beginner\'s Guide',
      tags: ['sound-signature', 'headphones', 'audio-guide'],
      author: 'Raghav Somani',
      authorAvatar: 'https://cdn.shopify.com/s/files/1/0153/8863/files/Headphone-Zone-Raghav-Somani-Founder-CEO_400x.jpg?v=**********',
      authorTitle: 'Founder & CEO',
      publishDate: new Date('2023-05-15'),
      readTime: 8
    },
    {
      id: 2,
      title: 'Wired vs Wireless Headphones: Which Should You Choose?',
      slug: 'wired-vs-wireless-headphones',
      excerpt: 'Explore the pros and cons of wired and wireless headphones to make an informed decision.',
      content: `<p>The debate between wired and wireless headphones continues to evolve as technology advances. Let's break down the key differences to help you decide which is right for you.</p>
      <h2>Wired Headphones: The Classic Choice</h2>
      <p>Wired headphones connect directly to your device through a physical cable, typically with a 3.5mm jack or USB-C connector.</p>
      <h3>Advantages of Wired Headphones</h3>
      <ul>
        <li><strong>Superior Sound Quality</strong>: Wired connections generally provide better audio quality with no compression or latency issues.</li>
        <li><strong>No Battery Concerns</strong>: Wired headphones don't need charging, so you never have to worry about running out of battery.</li>
        <li><strong>Cost-Effective</strong>: You can often get better sound quality for your money with wired options.</li>
        <li><strong>Zero Latency</strong>: Perfect for gaming and video production where timing is critical.</li>
      </ul>
      <h3>Disadvantages of Wired Headphones</h3>
      <ul>
        <li><strong>Limited Mobility</strong>: The cable restricts your movement and can get tangled.</li>
        <li><strong>Compatibility Issues</strong>: Many modern smartphones no longer include headphone jacks.</li>
        <li><strong>Cable Durability</strong>: Cables can wear out or break over time.</li>
      </ul>
      <h2>Wireless Headphones: The Modern Convenience</h2>
      <p>Wireless headphones connect to your devices via Bluetooth, offering freedom from cables.</p>
      <h3>Advantages of Wireless Headphones</h3>
      <ul>
        <li><strong>Freedom of Movement</strong>: No cables means greater mobility and convenience.</li>
        <li><strong>Universal Compatibility</strong>: Works with virtually any Bluetooth-enabled device.</li>
        <li><strong>Clean Aesthetic</strong>: No cables means a cleaner look and no tangling issues.</li>
      </ul>
      <h3>Disadvantages of Wireless Headphones</h3>
      <ul>
        <li><strong>Battery Dependency</strong>: Requires regular charging and battery life diminishes over time.</li>
        <li><strong>Potential Audio Quality Compromise</strong>: Bluetooth compression can affect sound quality.</li>
        <li><strong>Higher Cost</strong>: Generally more expensive than wired equivalents of similar sound quality.</li>
        <li><strong>Latency</strong>: May experience slight delays between video and audio.</li>
      </ul>`,
      imageUrl: 'https://cdn.shopify.com/s/files/1/0153/8863/files/Headphone-Zone-Wired-vs-Wireless-Banner_2000x.jpg?v=1651151051',
      category: 'Buying Guides',
      tags: ['wired-headphones', 'wireless-headphones', 'bluetooth', 'buying-guide'],
      author: 'Tanya Sharma',
      authorAvatar: 'https://cdn.shopify.com/s/files/1/0153/8863/files/Headphone-Zone-Tanya-Sharma-Content-Writer_400x.jpg?v=**********',
      authorTitle: 'Content Writer',
      publishDate: new Date('2023-06-22'),
      readTime: 10
    },
    {
      id: 3,
      title: 'Understanding IEMs: In-Ear Monitors Explained',
      slug: 'understanding-iems-in-ear-monitors-explained',
      excerpt: 'Dive into the world of IEMs and discover why audiophiles love these compact earphones.',
      content: `<p>In-Ear Monitors (IEMs) have become increasingly popular among audiophiles and music professionals. Let's explore what makes them special.</p>
      <h2>What Are IEMs?</h2>
      <p>In-Ear Monitors, or IEMs, are earphones that fit directly into the ear canal, creating a seal that isolates external noise. Unlike regular earbuds that sit in the outer ear, IEMs provide better isolation and typically superior sound quality.</p>
      <h2>Types of IEMs</h2>
      <h3>Dynamic Driver IEMs</h3>
      <p>These use a single dynamic driver similar to traditional speakers but miniaturized. They excel at producing powerful bass and a cohesive sound signature.</p>
      <h3>Balanced Armature IEMs</h3>
      <p>These use one or more balanced armature drivers, which are smaller and more precise than dynamic drivers. They excel at producing detailed mids and highs but sometimes lack bass impact.</p>
      <h3>Hybrid IEMs</h3>
      <p>These combine dynamic and balanced armature drivers to get the best of both worlds: powerful bass from dynamic drivers and detailed mids and highs from balanced armatures.</p>
      <h3>Planar Magnetic IEMs</h3>
      <p>A newer technology in the IEM world, planar magnetic drivers offer exceptional detail and speed with a more spacious soundstage.</p>
      <h2>Benefits of IEMs</h2>
      <ul>
        <li><strong>Portability</strong>: Compact and easy to carry anywhere.</li>
        <li><strong>Noise Isolation</strong>: The seal in your ear canal naturally blocks external noise.</li>
        <li><strong>Sound Quality</strong>: Can deliver audiophile-grade sound in a tiny package.</li>
        <li><strong>Comfort</strong>: When properly fitted, can be worn for hours without discomfort.</li>
      </ul>`,
      imageUrl: 'https://cdn.shopify.com/s/files/1/0153/8863/files/Headphone-Zone-IEMs-Explained-Banner_2000x.jpg?v=1651151051',
      category: 'Tech Explained',
      tags: ['iems', 'in-ear-monitors', 'earphones', 'audio-technology'],
      author: 'Raghav Somani',
      authorAvatar: 'https://cdn.shopify.com/s/files/1/0153/8863/files/Headphone-Zone-Raghav-Somani-Founder-CEO_400x.jpg?v=**********',
      authorTitle: 'Founder & CEO',
      publishDate: new Date('2023-07-10'),
      readTime: 7
    },
    {
      id: 4,
      title: 'How to Care for Your Headphones: Maintenance Tips',
      slug: 'how-to-care-for-your-headphones',
      excerpt: 'Learn how to properly maintain your headphones to ensure they last longer and sound better.',
      content: `<p>Proper maintenance of your headphones not only extends their lifespan but also ensures consistent sound quality. Here's how to care for your audio investment.</p>
      <h2>Regular Cleaning</h2>
      <p>Dirt, dust, and earwax can affect both the sound quality and hygiene of your headphones.</p>
      <h3>For Over-Ear and On-Ear Headphones</h3>
      <ul>
        <li>Wipe ear pads and headband with a slightly damp microfiber cloth</li>
        <li>For leather/protein leather pads, use appropriate leather cleaner occasionally</li>
        <li>Remove and wash detachable ear pads according to manufacturer instructions</li>
        <li>Use compressed air to clean grilles and hard-to-reach areas</li>
      </ul>
      <h3>For IEMs and Earbuds</h3>
      <ul>
        <li>Remove ear tips and wash with mild soap and water</li>
        <li>Use a soft brush to remove debris from nozzles</li>
        <li>For stubborn earwax, use specialized cleaning tools</li>
      </ul>
      <h2>Proper Storage</h2>
      <p>How you store your headphones when not in use significantly impacts their longevity.</p>
      <ul>
        <li>Always use the provided case or stand</li>
        <li>Avoid extreme temperatures and humidity</li>
        <li>Don't wrap cables tightly around headphones</li>
        <li>Use the proper coiling technique for cables to prevent damage</li>
      </ul>
      <h2>Cable Management</h2>
      <p>For wired headphones, cable damage is one of the most common issues.</p>
      <ul>
        <li>Coil cables loosely using the over-under technique</li>
        <li>Avoid sharp bends, especially near connectors</li>
        <li>Don't pull on the cable to unplug; grip the connector</li>
        <li>Consider a cable replacement if you notice intermittent sound</li>
      </ul>`,
      imageUrl: 'https://cdn.shopify.com/s/files/1/0153/8863/files/Headphone-Zone-Headphone-Care-Banner_2000x.jpg?v=1651151051',
      category: 'Beginner\'s Guide',
      tags: ['headphone-maintenance', 'cleaning', 'care-guide', 'longevity'],
      author: 'Tanya Sharma',
      authorAvatar: 'https://cdn.shopify.com/s/files/1/0153/8863/files/Headphone-Zone-Tanya-Sharma-Content-Writer_400x.jpg?v=**********',
      authorTitle: 'Content Writer',
      publishDate: new Date('2023-08-05'),
      readTime: 6
    },
    {
      id: 5,
      title: 'The Rise of Hi-Res Audio: Is It Worth the Upgrade?',
      slug: 'rise-of-hi-res-audio',
      excerpt: 'Explore the world of high-resolution audio and whether it makes a noticeable difference in your listening experience.',
      content: `<p>High-resolution audio has been gaining popularity among audiophiles, but is it truly worth investing in? Let's break down what hi-res audio is and whether you should upgrade.</p>
      <h2>What is Hi-Res Audio?</h2>
      <p>High-resolution audio refers to audio files that have a higher sampling rate and bit depth than CDs, which typically have a 44.1kHz sampling rate and 16-bit depth. Hi-res audio files usually have sampling rates of 96kHz or 192kHz and bit depths of 24-bit or higher.</p>
      <h2>The Technical Benefits</h2>
      <h3>Extended Frequency Response</h3>
      <p>While human hearing typically ranges from 20Hz to 20kHz, hi-res audio can reproduce frequencies beyond this range, which some argue contributes to a more natural sound reproduction.</p>
      <h3>Improved Dynamic Range</h3>
      <p>The higher bit depth of hi-res audio (24-bit vs. 16-bit) allows for a much wider dynamic range, capturing more subtle details in both quiet and loud passages.</p>
      <h3>Lower Noise Floor</h3>
      <p>Hi-res audio has a significantly lower noise floor, resulting in a cleaner, more detailed sound with less background hiss.</p>
      <h2>Can You Actually Hear the Difference?</h2>
      <p>This is where the debate gets interesting. Studies have shown mixed results:</p>
      <ul>
        <li>Some blind tests indicate that even experienced listeners struggle to consistently identify hi-res audio compared to CD-quality audio</li>
        <li>Other studies suggest that while the difference may be subtle, it becomes more apparent with high-quality equipment and trained ears</li>
        <li>The type of music and recording quality plays a significant role in whether hi-res makes a noticeable difference</li>
      </ul>`,
      imageUrl: 'https://cdn.shopify.com/s/files/1/0153/8863/files/Headphone-Zone-Hi-Res-Audio-Banner_2000x.jpg?v=1651151051',
      category: 'Tech Explained',
      tags: ['hi-res-audio', 'high-resolution', 'audio-quality', 'digital-audio'],
      author: 'Raghav Somani',
      authorAvatar: 'https://cdn.shopify.com/s/files/1/0153/8863/files/Headphone-Zone-Raghav-Somani-Founder-CEO_400x.jpg?v=**********',
      authorTitle: 'Founder & CEO',
      publishDate: new Date('2023-09-12'),
      readTime: 9
    },
    {
      id: 6,
      title: 'Interview: Behind the Scenes with Focal Headphones',
      slug: 'interview-behind-scenes-focal-headphones',
      excerpt: 'An exclusive interview with Focal\'s lead acoustic engineer on what makes their headphones special.',
      content: `<p>We had the opportunity to speak with Jean-Michel Bonin, Lead Acoustic Engineer at Focal, about the company's approach to headphone design and what sets them apart in the audiophile market.</p>
      <h2>The Focal Philosophy</h2>
      <p><strong>Headphone Zone:</strong> What's the core philosophy behind Focal's headphone designs?</p>
      <p><strong>Jean-Michel Bonin:</strong> At Focal, we believe in creating headphones that reproduce music with absolute fidelity to the original recording. Our philosophy is centered around three pillars: innovation in driver technology, meticulous attention to materials, and extensive listening tests throughout the development process. We don't just aim for impressive specifications; we want an emotional connection to the music.</p>
      <h2>The Beryllium Advantage</h2>
      <p><strong>Headphone Zone:</strong> Focal is known for using beryllium in your high-end drivers. Why this material?</p>
      <p><strong>Jean-Michel Bonin:</strong> Beryllium offers an exceptional combination of lightness and rigidity that's unmatched by other materials. Its high rigidity-to-weight ratio allows the driver to move extremely quickly and precisely, resulting in outstanding transient response and minimal distortion. This is why our Utopia headphones, which feature pure beryllium drivers, can reproduce such incredible detail and dynamics. It's challenging and expensive to work with, but the sonic results justify the effort.</p>
      <h2>From Speakers to Headphones</h2>
      <p><strong>Headphone Zone:</strong> How has Focal's experience in high-end speakers influenced your headphone development?</p>
      <p><strong>Jean-Michel Bonin:</strong> Our decades of speaker development have given us deep expertise in acoustics and driver design that directly translates to headphones. The M-shaped dome design that we use in our best speakers inspired the same profile in our headphone drivers. We've also applied our knowledge of cabinet resonance control to headphone cup design, using similar principles to minimize unwanted vibrations and colorations.</p>`,
      imageUrl: 'https://cdn.shopify.com/s/files/1/0153/8863/files/Headphone-Zone-Focal-Interview-Banner_2000x.jpg?v=1651151051',
      category: 'Interviews',
      tags: ['focal', 'beryllium', 'high-end-audio', 'interview'],
      author: 'Tanya Sharma',
      authorAvatar: 'https://cdn.shopify.com/s/files/1/0153/8863/files/Headphone-Zone-Tanya-Sharma-Content-Writer_400x.jpg?v=**********',
      authorTitle: 'Content Writer',
      publishDate: new Date('2023-10-18'),
      readTime: 12
    },
    {
      id: 7,
      title: 'The Best Budget Audiophile Headphones of 2023',
      slug: 'best-budget-audiophile-headphones-2023',
      excerpt: 'Discover our top picks for audiophile-quality headphones that won\'t break the bank.',
      content: `<p>You don't need to spend a fortune to get excellent sound quality. Here are our top picks for audiophile headphones under ₹10,000 in 2023.</p>
      <h2>1. KZ ZSN Pro X</h2>
      <p>The KZ ZSN Pro X continues to be one of the best values in the IEM world, offering a detailed, V-shaped sound signature that works well with most modern music genres.</p>
      <h3>Key Features:</h3>
      <ul>
        <li>Hybrid driver configuration with 1 dynamic driver and 1 balanced armature</li>
        <li>Detachable cable with 2-pin connector</li>
        <li>Metal faceplate with attractive design</li>
        <li>Excellent clarity and detail for the price</li>
      </ul>
      <p><strong>Price:</strong> ₹1,999</p>
      <h2>2. Moondrop Aria</h2>
      <p>The Moondrop Aria has become a benchmark for budget audiophile IEMs, offering a balanced, slightly warm sound signature that's incredibly natural and musical.</p>
      <h3>Key Features:</h3>
      <ul>
        <li>10mm Liquid Crystal Polymer dynamic driver</li>
        <li>Harman-inspired tuning with excellent tonal balance</li>
        <li>Elegant metal construction with matte black finish</li>
        <li>Detachable MMCX cable</li>
      </ul>
      <p><strong>Price:</strong> ₹7,999</p>
      <h2>3. Philips SHP9500</h2>
      <p>These open-back headphones offer a spacious, detailed sound that rivals much more expensive models, making them a favorite among budget-conscious audiophiles.</p>
      <h3>Key Features:</h3>
      <ul>
        <li>50mm neodymium drivers</li>
        <li>Open-back design for wide soundstage</li>
        <li>Lightweight and comfortable for long listening sessions</li>
        <li>Neutral sound signature with slight emphasis on mids</li>
      </ul>
      <p><strong>Price:</strong> ₹9,499</p>`,
      imageUrl: 'https://cdn.shopify.com/s/files/1/0153/8863/files/Headphone-Zone-Budget-Audiophile-Banner_2000x.jpg?v=1651151051',
      category: 'Buying Guides',
      tags: ['budget-audiophile', 'affordable', 'best-of-2023', 'value'],
      author: 'Raghav Somani',
      authorAvatar: 'https://cdn.shopify.com/s/files/1/0153/8863/files/Headphone-Zone-Raghav-Somani-Founder-CEO_400x.jpg?v=**********',
      authorTitle: 'Founder & CEO',
      publishDate: new Date('2023-11-05'),
      readTime: 8
    },
    {
      id: 8,
      title: 'Headphone Zone Launches Exclusive Partnership with Campfire Audio',
      slug: 'headphone-zone-campfire-audio-partnership',
      excerpt: 'We\'re excited to announce our exclusive partnership with premium IEM manufacturer Campfire Audio.',
      content: `<p>Headphone Zone is thrilled to announce our exclusive partnership with Campfire Audio, bringing their handcrafted premium IEMs to audiophiles across India.</p>
      <h2>About Campfire Audio</h2>
      <p>Based in Portland, Oregon, Campfire Audio has established itself as one of the most innovative and respected manufacturers of high-end in-ear monitors. Founded by Ken Ball in 2015, the company is known for its handcrafted approach, unique acoustic designs, and distinctive aesthetic.</p>
      <p>Each Campfire Audio IEM is assembled by hand in their Portland workshop, reflecting their commitment to quality and craftsmanship. Their products range from the entry-level Satsuma to the flagship Solaris, offering options for audiophiles at various price points.</p>
      <h2>Available Models</h2>
      <p>As part of this exclusive partnership, Headphone Zone will offer the complete range of Campfire Audio IEMs, including:</p>
      <ul>
        <li><strong>Campfire Audio Solaris 2020</strong> - The flagship model featuring a hybrid design with one dynamic driver and three balanced armature drivers</li>
        <li><strong>Campfire Audio Andromeda 2020</strong> - Their iconic five balanced armature IEM with a distinctive angular design</li>
        <li><strong>Campfire Audio Mammoth</strong> - A bass-focused hybrid IEM with powerful low-end response</li>
        <li><strong>Campfire Audio Holocene</strong> - A detail-oriented triple balanced armature IEM</li>
        <li><strong>Campfire Audio Honeydew</strong> - A single dynamic driver IEM with a warm, engaging sound</li>
        <li><strong>Campfire Audio Satsuma</strong> - An entry-level single balanced armature IEM with a balanced sound signature</li>
      </ul>
      <h2>Launch Event</h2>
      <p>To celebrate this partnership, Headphone Zone will be hosting a special launch event at our experience center in Mumbai on December 15, 2023. Attendees will have the opportunity to experience the entire Campfire Audio lineup and meet with representatives from both companies.</p>`,
      imageUrl: 'https://cdn.shopify.com/s/files/1/0153/8863/files/Headphone-Zone-Campfire-Audio-Banner_2000x.jpg?v=1651151051',
      category: 'News & Events',
      tags: ['campfire-audio', 'partnership', 'premium-iems', 'launch'],
      author: 'Raghav Somani',
      authorAvatar: 'https://cdn.shopify.com/s/files/1/0153/8863/files/Headphone-Zone-Raghav-Somani-Founder-CEO_400x.jpg?v=**********',
      authorTitle: 'Founder & CEO',
      publishDate: new Date('2023-12-01'),
      readTime: 5
    }
  ];

  constructor() { }

  getPosts(): Observable<BlogPost[]> {
    return of(this.posts);
  }

  getPostBySlug(slug: string): Observable<BlogPost> {
    const post = this.posts.find(p => p.slug === slug);
    if (post) {
      return of(post);
    }
    throw new Error(`Post with slug "${slug}" not found`);
  }

  getPostsByCategory(category: string): Observable<BlogPost[]> {
    if (category === 'All') {
      return this.getPosts();
    }
    return of(this.posts.filter(p => p.category === category));
  }

  getPostsByTag(tag: string): Observable<BlogPost[]> {
    return of(this.posts.filter(p => p.tags.includes(tag)));
  }

  getRecentPosts(count: number = 5): Observable<BlogPost[]> {
    return of([...this.posts]
      .sort((a, b) => b.publishDate.getTime() - a.publishDate.getTime())
      .slice(0, count));
  }
}
