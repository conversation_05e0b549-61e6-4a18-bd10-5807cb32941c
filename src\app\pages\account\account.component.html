<app-header></app-header>

<div class="account-container">
  <div class="container mx-auto px-4 py-8">
    <div class="page-header mb-8">
      <h1 class="page-title">My Account</h1>
      <p class="page-subtitle">Manage your orders, wishlist, and account details</p>
    </div>

    <div class="account-content">
      <!-- Account Sidebar -->
      <div class="account-sidebar">
        <div class="user-info">
          <div class="avatar">
            <img [src]="user?.avatar || 'assets/images/avatars/default-avatar.jpg'" [alt]="user?.firstName">
          </div>
          <div class="user-details">
            <h3 class="user-name">{{ user?.firstName }} {{ user?.lastName }}</h3>
            <p class="user-email">{{ user?.email }}</p>
          </div>
        </div>

        <nav class="account-nav">
          <ul>
            <li *ngFor="let item of navItems">
              <button 
                [class.active]="activeTab === item.id"
                (click)="setActiveTab(item.id)"
                class="nav-item">
                <mat-icon>{{ item.icon }}</mat-icon>
                <span>{{ item.label }}</span>
              </button>
            </li>
          </ul>
        </nav>

        <button mat-stroked-button color="warn" class="logout-button" (click)="logout()">
          <mat-icon>exit_to_app</mat-icon>
          Logout
        </button>
      </div>

      <!-- Account Content -->
      <div class="account-main">
        <!-- Orders Tab -->
        <div *ngIf="activeTab === 'orders'" class="tab-content">
          <div class="tab-header">
            <h2 class="tab-title">My Orders</h2>
            <p class="tab-subtitle">View and track your orders</p>
          </div>

          <div class="empty-state">
            <img src="assets/images/empty-states/empty-orders.svg" alt="No orders" class="empty-icon">
            <h3 class="empty-title">No orders yet</h3>
            <p class="empty-message">You haven't placed any orders yet. Start shopping to see your orders here.</p>
            <button mat-raised-button color="primary" routerLink="/collections/all">
              Start Shopping
            </button>
          </div>
        </div>

        <!-- Wishlist Tab -->
        <div *ngIf="activeTab === 'wishlist'" class="tab-content">
          <div class="tab-header">
            <h2 class="tab-title">My Wishlist</h2>
            <p class="tab-subtitle">Products you've saved for later</p>
          </div>

          <div class="empty-state">
            <img src="assets/images/empty-states/empty-wishlist.svg" alt="Empty wishlist" class="empty-icon">
            <h3 class="empty-title">Your wishlist is empty</h3>
            <p class="empty-message">Save items you like by clicking the heart icon on product pages.</p>
            <button mat-raised-button color="primary" routerLink="/collections/all">
              Explore Products
            </button>
          </div>
        </div>

        <!-- Addresses Tab -->
        <div *ngIf="activeTab === 'addresses'" class="tab-content">
          <div class="tab-header">
            <h2 class="tab-title">My Addresses</h2>
            <p class="tab-subtitle">Manage your shipping and billing addresses</p>
          </div>

          <div class="addresses-container">
            <div class="address-card add-address">
              <button mat-stroked-button class="add-address-button">
                <mat-icon>add</mat-icon>
                Add New Address
              </button>
            </div>
          </div>
        </div>

        <!-- Profile Tab -->
        <div *ngIf="activeTab === 'profile'" class="tab-content">
          <div class="tab-header">
            <h2 class="tab-title">My Profile</h2>
            <p class="tab-subtitle">Manage your personal information</p>
          </div>

          <div class="profile-form">
            <div class="form-row">
              <mat-form-field appearance="outline">
                <mat-label>First Name</mat-label>
                <input matInput [value]="user?.firstName || ''">
              </mat-form-field>

              <mat-form-field appearance="outline">
                <mat-label>Last Name</mat-label>
                <input matInput [value]="user?.lastName || ''">
              </mat-form-field>
            </div>

            <mat-form-field appearance="outline">
              <mat-label>Email</mat-label>
              <input matInput [value]="user?.email || ''" disabled>
            </mat-form-field>

            <mat-form-field appearance="outline">
              <mat-label>Phone Number</mat-label>
              <input matInput placeholder="Enter your phone number">
            </mat-form-field>

            <div class="form-actions">
              <button mat-raised-button color="primary">
                Save Changes
              </button>
            </div>
          </div>
        </div>

        <!-- Settings Tab -->
        <div *ngIf="activeTab === 'settings'" class="tab-content">
          <div class="tab-header">
            <h2 class="tab-title">Account Settings</h2>
            <p class="tab-subtitle">Manage your account preferences</p>
          </div>

          <div class="settings-section">
            <h3 class="settings-title">Password</h3>
            <div class="settings-content">
              <button mat-stroked-button color="primary">
                Change Password
              </button>
            </div>
          </div>

          <div class="settings-section">
            <h3 class="settings-title">Email Preferences</h3>
            <div class="settings-content">
              <mat-checkbox color="primary" checked>Receive order updates</mat-checkbox>
              <mat-checkbox color="primary" checked>Receive promotional emails</mat-checkbox>
              <mat-checkbox color="primary">Receive newsletter</mat-checkbox>
            </div>
          </div>

          <div class="settings-section">
            <h3 class="settings-title">Delete Account</h3>
            <div class="settings-content">
              <p class="settings-description">
                Permanently delete your account and all associated data. This action cannot be undone.
              </p>
              <button mat-stroked-button color="warn">
                Delete Account
              </button>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</div>

<app-footer></app-footer>
