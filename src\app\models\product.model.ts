export type SoundSignature = 'balanced' | 'warm' | 'bright' | 'v-shaped' | 'bass-focused' | 'vocal-focused' | 'neutral';

export type ProductBadge = 'value-for-money' | 'ideal-for-beginners' | 'staff-pick' | 'award-winner' | 'best-seller' | 'limited-edition';

export interface Product {
  id: number;
  name: string;
  description: string;
  price: number;
  discountedPrice?: number;
  imageUrl: string;
  category: string;
  rating: number;
  reviewCount: number;
  stock: number;
  isNew?: boolean;
  isHot?: boolean;
  tags?: string[];
  soundSignature?: SoundSignature;
  badges?: ProductBadge[];
  technicalSpecs?: {
    driverType?: string;
    driverSize?: string;
    impedance?: string;
    sensitivity?: string;
    frequency?: string;
    cableLength?: string;
    weight?: string;
    connector?: string;
  };
}

export interface FeaturedDeal extends Product {
  endsAt: Date;
  progress: number;
  originalPrice: number;
  soldCount: number;
}

export interface PurchaseNotification {
  type?:
    | 'copy'
    | 'stock-warning'
    | 'order-confirmation'
    | 'error'
    | 'viewer'
    | 'purchase'
    | 'success';
  customerName?: string;
  location?: string;
  productName: string;
  additionalInfo?: string;
  viewerCount?: number;
  timestamp: Date;
}

export interface CartItem {
  product: Product;
  quantity: number;
}

export interface OrderSummary {
  subtotal: number;
  shipping: number;
  tax: number;
  total: number;
  items: CartItem[];
}
