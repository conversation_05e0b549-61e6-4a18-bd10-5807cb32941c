<div *ngIf="(notification$ | async) as notification" [@toastAnimation]="(isActive$ | async) ? 'visible' : 'void'"
  [class]="getBackgroundClass(notification)"
  class="fixed bottom-4 right-4 z-50 rounded-lg shadow-lg overflow-hidden max-w-sm" role="alert" aria-live="polite">
  <div class="p-4 flex items-start space-x-4">
    <!-- Icon -->
    <div class="flex-shrink-0 w-10 h-10 rounded-full flex items-center justify-center" [@iconAnimation]>
      <mat-icon [class]="getIconClass(notification)" class="text-2xl">
        {{ getIcon(notification) }}
      </mat-icon>
    </div>

    <!-- Content -->
    <div class="flex-1 pt-1">
      <!-- Purchase notification -->
      <ng-container *ngIf="!notification.type">
        <p class="text-sm font-medium text-gray-900">
          {{ notification.customerName }} from {{ notification.location }}
        </p>
        <p class="mt-1 text-sm text-gray-600">
          just purchased {{ notification.productName }}
        </p>
      </ng-container>

      <!-- Viewer notification -->
      <ng-container *ngIf="notification.type === 'viewer'">
        <p class="text-sm font-medium text-gray-900">
          {{ notification.viewerCount }} people are viewing
        </p>
        <p class="mt-1 text-sm text-gray-600">
          {{ notification.productName }}
        </p>
      </ng-container>

      <!-- Stock warning -->
      <ng-container *ngIf="notification.type === 'stock-warning'">
        <p class="text-sm font-medium text-gray-900">
          Low Stock Alert
        </p>
        <p class="mt-1 text-sm text-gray-600">
          {{ notification.additionalInfo }}
        </p>
      </ng-container>

      <!-- Order confirmation -->
      <ng-container *ngIf="notification.type === 'order-confirmation'">
        <p class="text-sm font-medium text-gray-900">
          Order Confirmed
        </p>
        <p class="mt-1 text-sm text-gray-600">
          {{ notification.additionalInfo }}
        </p>
      </ng-container>

      <!-- Copy success -->
      <ng-container *ngIf="notification.type === 'copy'">
        <p class="text-sm font-medium text-gray-900">
          {{ notification.productName }}
        </p>
      </ng-container>

      <!-- Error message -->
      <ng-container *ngIf="notification.type === 'error'">
        <p class="text-sm font-medium text-gray-900">
          Error
        </p>
        <p class="mt-1 text-sm text-gray-600">
          {{ notification.productName }}
        </p>
      </ng-container>

      <!-- Timestamp -->
      <p class="mt-1 text-xs text-gray-500" *ngIf="notification.timestamp">
        {{ formatTimeAgo(notification.timestamp) }}
      </p>
    </div>
  </div>

  <!-- Progress bar for auto-dismiss -->
  <div class="h-1 bg-gray-100">
    <div class="h-full bg-primary transition-all duration-[5000ms] ease-linear" [class.w-0]="!(isActive$ | async)"
      class="w-full"></div>
  </div>
</div>